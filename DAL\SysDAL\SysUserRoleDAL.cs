using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;

namespace DAL.SysDAL
{
    /// <summary>
    /// 用户角色数据访问层实现
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="context">数据库上下文</param>
    public class SysUserRoleDAL(MyContext context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 获取用户角色查询对象
        /// </summary>
        /// <returns>查询对象</returns>
        public IQueryable<SysUserRole> GetQueryable()
        => _context.SysUserRoles.AsQueryable();


        /// <summary>
        /// 添加用户角色
        /// </summary>
        /// <param name="userRole">用户角色实体</param>
        /// <returns>操作结果</returns>
        public async Task<bool> AddAsync(SysUserRole userRole)
        {
            await _context.SysUserRoles.AddAsync(userRole);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 批量添加用户角色
        /// </summary>
        /// <param name="userRoles">用户角色实体列表</param>
        /// <returns>操作结果</returns>
        public async Task<bool> AddRangeAsync(List<SysUserRole> userRoles)
        {
            await _context.SysUserRoles.AddRangeAsync(userRoles);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 删除用户角色
        /// </summary>
        /// <param name="userRole">用户角色实体</param>
        /// <returns>操作结果</returns>
        public async Task<bool> DeleteAsync(SysUserRole userRole)
        {
            _context.SysUserRoles.Remove(userRole);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 批量删除用户角色
        /// </summary>
        /// <param name="userRoles">用户角色实体列表</param>
        /// <returns>操作结果</returns>
        public async Task<bool> DeleteRangeAsync(List<SysUserRole> userRoles)
        {
            _context.SysUserRoles.RemoveRange(userRoles);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取用户的角色ID列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>角色ID列表</returns>
        public Task<List<string>> GetUserRoleIdsAsync(string userId)
        => _context.SysUserRoles
                .Where(ur => ur.UserId == userId)
                .Select(ur => ur.RoleId)
                .ToListAsync();


        /// <summary>
        /// 获取角色的用户ID列表
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>用户ID列表</returns>
        public Task<List<string>> GetRoleUserIdsAsync(string roleId)
        => _context.SysUserRoles
                .Where(ur => ur.RoleId == roleId)
                .Select(ur => ur.UserId)
                .ToListAsync();


        /// <summary>
        /// 删除用户的所有角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        public async Task<bool> DeleteUserRolesAsync(string userId)
        {
            var userRoles = await _context.SysUserRoles
                .Where(ur => ur.UserId == userId)
                .ToListAsync();

            if (userRoles.Count > 0)
            {
                _context.SysUserRoles.RemoveRange(userRoles);
                return await _context.SaveChangesAsync() > 0;
            }

            return true;
        }

        /// <summary>
        /// 删除角色的所有用户
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>操作结果</returns>
        public async Task<bool> DeleteRoleUsersAsync(string roleId)
        {
            var userRoles = await _context.SysUserRoles
                .Where(ur => ur.RoleId == roleId)
                .ToListAsync();

            if (userRoles.Count > 0)
            {
                _context.SysUserRoles.RemoveRange(userRoles);
                return await _context.SaveChangesAsync() > 0;
            }

            return true;
        }
    }
}