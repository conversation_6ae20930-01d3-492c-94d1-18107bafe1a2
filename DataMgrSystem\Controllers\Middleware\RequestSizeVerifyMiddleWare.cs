﻿using Newtonsoft.Json;
namespace DataMgrSystem.Controllers.Middleware
{
    /// <summary>
    /// 验证请求是否超过设置的最大值
    /// </summary>
    public class RequestSizeVerifyMiddleWare(RequestDelegate next)
    {
        private readonly RequestDelegate _next = next;

        public async Task InvokeAsync(HttpContext context)
        {
            if (!await Verify(context)) return;
            await _next(context);
        }
        /// <summary>
        /// 验证请求是否超过设置的最大值
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>是否验证通过</returns>
        private static async Task<bool> Verify(HttpContext context)
        {
            Type _features = context.Features.GetType();
            if (_features.Name != "Http2Stream`1") return true;
            // 获取请求体大小
            var inputRemaining = _features.GetProperty("InputRemaining")?.GetValue(context.Features);
            if (inputRemaining == null) return true;
            // 获取服务器设置最大大小
            var maxRequestSize = _features.GetProperty("MaxRequestBodySize")?.GetValue(context.Features);
            if (maxRequestSize == null) return true;
            // 判断是否超过 最大大小
            if ((long)inputRemaining < (long)maxRequestSize) return true;
            // 判断请求是否被处理
            if (context.Response.HasStarted) return true;

            context.Response.StatusCode = 200;
            await context.Response.WriteAsync(JsonConvert.SerializeObject(new { code = 500, msg = $"请求体不能大于 {(long)maxRequestSize / 1024 / 1024}MB", success = false }));
            return false;
        }

    }

}
