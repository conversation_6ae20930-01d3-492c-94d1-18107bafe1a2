using Common.Autofac;
using DAL.Databases;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.SysDAL
{
    /// <summary>
    /// 按钮数据访问层
    /// 负责按钮相关的数据库操作
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="dbContext">数据库上下文</param>
    /// <param name="userDLL">用户数据访问层</param>
    /// <param name="roleDLL">角色数据访问层</param>
    [Dependency(DependencyType.Scoped)]
    public class SysButtonDAL(MyContext dbContext) : BaseQueryDLL<SysButton, SysButtonDAL.Queryable>(dbContext)
    {

        /// <summary>
        /// 按钮查询条件模型类
        /// 定义了按钮查询时可用的过滤条件
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 按钮ID
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? Id { get; set; }

            /// <summary>
            /// 按钮名称
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Name { get; set; }

            /// <summary>
            /// 权限编码
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? PermissionCode { get; set; }

            /// <summary>
            /// 创建时间排序标志
            /// 用于按创建时间降序排序
            /// </summary>
            [Query(QueryOperator.排序, columnName: "Id", orderDirection: OrderDirection.降序, orderPriority: 1)]
            public int? OrderById { get; set; } = 1;
        }


        /// <summary>
        /// 同步按钮数据
        /// </summary>
        /// <param name="permissionMethods">权限元数据列表</param>
        /// <param name="currentUserInfoDto">当前用户信息</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> SyncButtonsAsync(List<PermissionMetadataDto> permissionMethods)
        {
            try
            {
                // 开启事务
                using var transaction = await _dbContext.Database.BeginTransactionAsync();

                try
                {
                    // 获取数据库中的所有按钮
                    var existingButtons = await _dbContext.SysButtons.ToListAsync();

                    // 转换权限方法为按钮对象
                    var newButtons = permissionMethods.Select(p => new SysButton
                    {
                        Name = p.PermissionDescription,
                        PermissionCode = p.PermissionCode,
                        Description = p.PermissionDescription,
                    }).ToList();

                    // 处理需要更新的数据
                    var buttonsToUpdate = new List<SysButton>();
                    foreach (var newButton in newButtons)
                    {
                        var existingButton = existingButtons.FirstOrDefault(b => b.PermissionCode == newButton.PermissionCode);
                        if (existingButton != null)
                        {
                            // 更新现有按钮的属性
                            existingButton.Name = newButton.Name;
                            existingButton.Description = newButton.Description;

                            // 添加到更新列表
                            buttonsToUpdate.Add(existingButton);

                            // 从现有按钮列表中移除，剩下的将被视为需要删除的按钮
                            existingButtons.Remove(existingButton);
                        }
                    }

                    // 查找需要删除的按钮（数据库中有但新数据中没有的）
                    var buttonsToRemove = existingButtons;

                    // 执行删除操作
                    if (buttonsToRemove.Count != 0)
                        _dbContext.SysButtons.RemoveRange(buttonsToRemove);


                    // 执行更新操作
                    foreach (var button in buttonsToUpdate)
                    {
                        _dbContext.Entry(button).State = EntityState.Modified;
                    }

                    // 查找需要添加的按钮（新数据中有但数据库中没有的）
                    var buttonsToAdd = newButtons
                        .Where(n => !existingButtons.Any(e => e.PermissionCode == n.PermissionCode)
                               && !buttonsToUpdate.Any(u => u.PermissionCode == n.PermissionCode))
                        .ToList();

                    // 执行添加操作
                    if (buttonsToAdd.Count != 0)
                        await _dbContext.SysButtons.AddRangeAsync(buttonsToAdd);


                    // 保存更改
                    await _dbContext.SaveChangesAsync();

                    // 提交事务
                    await transaction.CommitAsync();
                    return true;
                }
                catch (Exception)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }












    }
}