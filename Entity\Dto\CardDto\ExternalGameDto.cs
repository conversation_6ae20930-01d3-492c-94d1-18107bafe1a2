namespace Entity.Dto.CardDto;

public class ExternalGameDto
{

}


/// <summary>
/// 游戏充值请求DTO
/// </summary>
public class GameRecharge_ResDto
{
    /// <summary>
    /// 订单编号，最大32位，全局唯一（可选，不传则自动生成）
    /// </summary>
    public string? OrderId { get; set; }

    /// <summary>
    /// 俱乐部ID
    /// </summary>
    public int HallId { get; set; }

    /// <summary>
    /// 操作者的游戏UID
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// 充值虚拟币（体力）数量
    /// </summary>
    public int Point { get; set; }

    /// <summary>
    /// 卡编号，最大32位
    /// </summary>
    public string[] CardNo { get; set; } = [];

    /// <summary>
    /// 卡归属游戏UID
    /// </summary>
    public int CardOwnerUid { get; set; }

    /// <summary>
    /// 预留，默认为0，表示管理员发放，为1时可以不传card_owner_uid
    /// </summary>
    public int IsSystem { get; set; } = 0;
}

#pragma warning disable IDE1006 // 命名样式
/// <summary>
/// 游戏API请求DTO（内部使用）
/// </summary>
public class GameApi_ReqDto
{
    /// <summary>
    /// 接口名称固定不变
    /// </summary>
    public string api { get; set; } = "api_recharge_hall_bank_money";

    /// <summary>
    /// 订单编号，最大32位，全局唯一
    /// </summary>
    public string orderid { get; set; } = string.Empty;

    /// <summary>
    /// 俱乐部ID
    /// </summary>
    public int hall_id { get; set; }

    /// <summary>
    /// 操作者的游戏UID
    /// </summary>
    public int userid { get; set; }

    /// <summary>
    /// 充值虚拟币（体力）数量
    /// </summary>
    public int point { get; set; }

    /// <summary>
    /// 卡编号，最大32位
    /// </summary>
    public string[] card_no { get; set; } = [];

    /// <summary>
    /// 卡归属游戏UID
    /// </summary>
    public int card_owner_uid { get; set; }

    /// <summary>
    /// 预留，默认为0，表示管理员发放，为1时可以不传card_owner_uid
    /// </summary>
    public int is_system { get; set; } = 0;
}


/// <summary>
/// 游戏API最终请求DTO
/// </summary>
public class GameApiFinal_ReqDto
{
    /// <summary>
    /// Base64编码的数据
    /// </summary>
    public string data { get; set; } = string.Empty;

    /// <summary>
    /// 签名
    /// </summary>
    public string sign { get; set; } = string.Empty;
}



/// <summary>
/// 游戏API响应DTO
/// </summary>
public class GameApi_ResDto
{
    /// <summary>
    /// 错误码(1成功)
    /// </summary>
    public int Code { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Data { get; set; } = string.Empty;
}





/// <summary>
/// 玩家转卡给服务器推送消息DTO
/// </summary>
public class PushCardToGame_ResDto
{

    /// <summary>
    /// 接口名称固定不变
    /// </summary>
    public string api { get; set; } = "app_card_transaction_notification";

    /// <summary>
    /// 源UID
    /// </summary>
    public int org_uid { get; set; }

    /// <summary>
    /// 目标UID
    /// </summary>
    public int target_uid { get; set; }

    /// <summary>
    /// 卡数量
    /// </summary>
    public int card_count { get; set; }

    /// <summary>
    /// 卡点数
    /// </summary>
    public int card_point { get; set; }
}


#pragma warning restore IDE1006 // 命名样式

