using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.OrganizationDto
{
    /// <summary>
    /// 用户部门职位关联DTO
    /// </summary>
    public class UserDepartmentPositionDto
    {
        public long Id { get; set; }
        public long UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public long DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public long PositionId { get; set; }
        public string PositionName { get; set; } = string.Empty;
        public bool IsPrimary { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 分配用户部门职位DTO
    /// </summary>
    public class AssignUserDepartmentPositionDto
    {
        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public string DepartmentId { get; set; } = string.Empty;

        [Required]
        public string PositionId { get; set; } = string.Empty;

        public bool IsPrimary { get; set; }

        public DateTime? StartTime { get; set; }
    }

    /// <summary>
    /// 更新用户部门职位关联DTO
    /// </summary>
    public class UpdateUserDepartmentPositionDto
    {
        public long Id { get; set; }

        [Required]
        public long UserId { get; set; }

        [Required]
        public long DepartmentId { get; set; }

        [Required]
        public long PositionId { get; set; }

        public bool IsPrimary { get; set; }

        public DateTime? StartTime { get; set; }

        public DateTime? EndTime { get; set; }
    }
}