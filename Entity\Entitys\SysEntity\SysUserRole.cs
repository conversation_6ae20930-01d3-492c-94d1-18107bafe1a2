using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.SysEntity
{
    /// <summary>
    /// 用户角色关联实体
    /// </summary>
    [Table("sys_user_role")]
    public class SysUserRole : BaseEntity_GUID
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        [Comment("用户ID")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 角色ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        [Comment("角色ID")]
        public string RoleId { get; set; } = string.Empty;

        /// <summary>
        /// 用户导航属性
        /// </summary>
        [ForeignKey(nameof(UserId))]
        [Comment("用户导航属性")]
        public virtual SysUser? User { get; set; }

        /// <summary>
        /// 角色导航属性
        /// </summary>
        [ForeignKey(nameof(RoleId))]
        [Comment("角色导航属性")]
        public virtual SysRole? Role { get; set; }
    }
}