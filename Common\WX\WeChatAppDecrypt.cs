﻿using Newtonsoft.Json;
using System.Net;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;

namespace Common.WX
{
    public class WeChatAppDecrypt
    {
        private static readonly string appId = "";

        private static readonly string appSecret = "";
        static WeChatAppDecrypt()
        {
            appId = WxSetting.AppId;
            appSecret = WxSetting.AppSecret;
        }

        /// 
        /// 获取 OpenId 和 SessionKey 的 Json 数据包
        /// 
        ///  客户端发来的 code
        /// Json 数据包 
        private static string GetOpenIdAndSessionKeyString(string code)
        {
            string temp = $"https://api.weixin.qq.com/sns/jscode2session?appid={appId}&secret={appSecret}&js_code={code}&grant_type=authorization_code";
            return GetResponse(temp);
        }


        /// <summary>
        /// 反序列化包含 OpenId 和 SessionKey 的 Json 数据包
        /// Json 数据包 
        /// 包含 OpenId 和 SessionKey 的类 
        /// </summary>
        /// <param name="loginInfo"></param>
        /// <returns></returns>
        public static OpenIdAndSessionKey? DecodeOpenIdAndSessionKey(WechatLoginInfo loginInfo)
        {
            try
            {
                var oiask = JsonConvert.DeserializeObject<OpenIdAndSessionKey>(GetOpenIdAndSessionKeyString(loginInfo.Code));
                if (oiask == null) return null;
                if (!String.IsNullOrEmpty(oiask.Errcode)) return null;
                return oiask;
            }
            catch (Exception)
            {
                //@TODO 写日志 获取微信OpenId报错
                return null;
            }

        }

        /// 
        /// 根据微信小程序平台提供的签名验证算法验证用户发来的数据是否有效
        /// 


        ///  公开的用户资料 
        ///  公开资料携带的签名信息 
        ///  从服务端获取的 SessionKey
        /// True：资料有效，False：资料无效 

        public static bool VaildateUserInfo(string rawData, string signature, string sessionKey)

        {

            // 创建 SHA1 签名类

            // 编码用于 SHA1 验证的源数据

            byte[] source = Encoding.UTF8.GetBytes(rawData + sessionKey);

            // 生成签名

            byte[] target = SHA1.HashData(source);

            // 转化为 string 类型，注意此处转化后是中间带短横杠的大写字母，需要剔除横杠转小写字母

            string result = BitConverter.ToString(target).Replace("-", "").ToLower();

            // 比对，输出验证结果

            return signature == result;

        }

        /// 
        /// 根据微信小程序平台提供的签名验证算法验证用户发来的数据是否有效
        ///  登陆信息 
        ///  从服务端获取的 SessionKey
        /// True：资料有效，False：资料无效 
        public static bool VaildateUserInfo(WechatLoginInfo loginInfo, string sessionKey)

        {

            return VaildateUserInfo(loginInfo.RawData, loginInfo.Signature, sessionKey);

        }

        /// 根据微信小程序平台提供的签名验证算法验证用户发来的数据是否有效
        ///  登陆信息 
        ///  包含 OpenId 和 SessionKey 的类 
        /// True：资料有效，False：资料无效 
        public static bool VaildateUserInfo(WechatLoginInfo loginInfo, OpenIdAndSessionKey idAndKey)

        {

            return VaildateUserInfo(loginInfo, idAndKey.Session_Key);

        }

        ///
        /// 根据微信小程序平台提供的解密算法解密数据
        ///  加密数据 
        ///  初始向量 
        ///  从服务端获取的 SessionKey
        public static WechatUserInfo? Decrypt(string encryptedData, string iv, string sessionKey)
        {
            try
            {
                // 使用 Aes.Create() 替代过时的 AesCryptoServiceProvider
                using var aes = Aes.Create();

                // 设置解密器参数
                aes.Mode = CipherMode.CBC;
                aes.BlockSize = 128;
                aes.Padding = PaddingMode.PKCS7;

                // 格式化待处理字符串
                byte[] byte_encryptedData = Convert.FromBase64String(encryptedData);
                byte[] byte_iv = Convert.FromBase64String(iv);
                byte[] byte_sessionKey = Convert.FromBase64String(sessionKey);

                aes.IV = byte_iv;
                aes.Key = byte_sessionKey;

                // 根据设置好的数据生成解密器实例
                using var transform = aes.CreateDecryptor();

                // 解密
                byte[] final = transform.TransformFinalBlock(byte_encryptedData, 0, byte_encryptedData.Length);

                // 生成结果
                string result = Encoding.UTF8.GetString(final);

                // 反序列化结果，生成用户信息实例
                var userInfo = JsonConvert.DeserializeObject<WechatUserInfo>(result);
                return userInfo;
            }
            catch (Exception)
            {
                //@TODO 写日志 解密失败
                return null;
            }
        }

        /// <summary>
        /// 根据微信小程序平台提供的解密算法解密数据，推荐直接使用此方法
        /// </summary>
        /// <param name="loginInfo">登录信息</param>
        /// <returns>用户信息，如果解密失败则返回 null</returns>
        public static WechatUserInfo? Decrypt(WechatLoginInfo? loginInfo)
        {
            if (loginInfo == null) return null;
            if (string.IsNullOrEmpty(loginInfo.Code)) return null;

            var oiask = DecodeOpenIdAndSessionKey(loginInfo);
            if (oiask == null) return null;

            if (!VaildateUserInfo(loginInfo, oiask)) return null;

            return Decrypt(loginInfo.EncryptedData, loginInfo.Iv, oiask.Session_Key);
        }

        /// <summary>
        /// 发送 GET 请求获取响应内容
        /// </summary>
        /// <param name="url">请求URL</param>
        /// <returns>响应内容，如果请求失败则返回空字符串</returns>
        private static string GetResponse(string url)
        {
            if (url.StartsWith("https"))
            {
                System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls;
            }

            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            try
            {
                HttpResponseMessage response = httpClient.GetAsync(url).Result;
                if (response.IsSuccessStatusCode)
                {
                    string result = response.Content.ReadAsStringAsync().Result;
                    return result ?? string.Empty;
                }
            }
            catch (Exception)
            {
                //@TODO 写日志 HTTP请求失败
            }

            return string.Empty;
        }

    }

    /// 微信小程序登录信息结构
    public class WechatLoginInfo

    {

        public string Code { get; set; } = "";

        public string EncryptedData { get; set; } = "";

        public string Iv { get; set; } = "";

        public string RawData { get; set; } = "";

        public string Signature { get; set; } = "";

    }

    /// 


    /// 微信小程序用户信息结构

    /// 


    public class WechatUserInfo

    {

        public string OpenId { get; set; } = "";

        public string NickName { get; set; } = "";

        public string Gender { get; set; } = "";

        public string City { get; set; } = "";

        public string Province { get; set; } = "";

        public string Country { get; set; } = "";

        public string AvatarUrl { get; set; } = "";

        public string UnionId { get; set; } = "";

        public Watermark Watermarks { get; set; } = new();

        public class Watermark

        {

            public string Appid { get; set; } = "";

            public string Timestamp { get; set; } = "";

        }

    }

    /// 


    /// 微信小程序从服务端获取的 OpenId 和 SessionKey 信息结构

    /// 


    public class OpenIdAndSessionKey

    {

        public string OpenId { get; set; } = "";

        public string Session_Key { get; set; } = "";

        public string Errcode { get; set; } = "";

        public string ErrMsg { get; set; } = "";

    }

}
