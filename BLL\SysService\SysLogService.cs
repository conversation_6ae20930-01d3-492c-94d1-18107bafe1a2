using Common.Autofac;
using Common.Log4Net;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;
using static DAL.Databases.EFHelper;

namespace BLL.SysService
{
    /// <summary>
    /// 日志服务实现类
    /// </summary>
    /// <remarks>
    /// 构造函数,通过依赖注入获取日志数据访问层实例
    /// </remarks>
    /// <param name="logDAL">日志数据访问层接口</param>
    [Dependency(DependencyType.Scoped)]
    public class SysLogService(SysLogDAL logDAL)
    {
        /// <summary>
        /// 日志数据访问层接口
        /// </summary>
        private readonly SysLogDAL _logDAL = logDAL;

        /// <summary>
        /// 缓存的JSON序列化选项，用于格式化输出
        /// </summary>
        private static readonly JsonSerializerOptions _indentedJsonOptions = new()
        {
            WriteIndented = true
        };

        /// <summary>
        /// 缓存的JSON序列化选项，用于业务日志
        /// </summary>
        private static readonly JsonSerializerOptions _businessJsonOptions = new()
        {
            WriteIndented = true,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };

        /// <summary>
        /// 记录操作日志（异步，不等待）
        /// </summary>
        /// <param name="module">模块名称</param>
        /// <param name="description">操作描述</param>
        /// <param name="level">日志级别</param>
        /// <param name="userId">用户ID</param>
        /// <param name="username">用户名</param>
        /// <param name="params">请求参数</param>
        /// <param name="result">操作结果</param>
        /// <param name="error">异常信息</param>
        /// <param name="ip">IP地址</param>
        /// <param name="path">请求路径</param>
        /// <param name="time">执行时间(毫秒)</param>
        /// <param name="logType">日志类型</param>
        public void Log(string module, string description, LogLevel level = LogLevel.Information,
            string? userId = null, string? username = null, object? @params = null, object? result = null, Exception? error = null,
            string? ip = null, string? path = null, long time = 0, string logType = "User")
        {
            // 异步写入日志，不等待
            _ = Task.Run(async () =>
            {
                try
                {
                    // 构建日志消息
                    var message = $"模块：{module}，操作：{description}";
                    if (@params != null)
                        message += $"，参数：{JsonSerializer.Serialize(@params)}";
                    if (result != null)
                        message += $"，结果：{JsonSerializer.Serialize(result)}";
                    if (error != null)
                        message += $"，异常：{error.Message}";

                    // 写入文件日志
                    switch (level)
                    {
                        case LogLevel.Error:
                            await LoggerHelper<SysLogService>.ErrorAsync(message, error);
                            break;
                        case LogLevel.Warning:
                            await LoggerHelper<SysLogService>.WarnAsync(message, error);
                            break;
                        default:
                            await LoggerHelper<SysLogService>.InfoAsync(message, error);
                            break;
                    }

                    // 写入数据库
                    var log = new SysLog
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        UserId = userId,
                        Username = username,
                        Operation = description,
                        Method = module,
                        Params = @params != null ? JsonSerializer.Serialize(@params) : null,
                        Time = time,
                        Ip = ip ?? "",
                        Path = path ?? "",
                        LogType = logType,
                        LogLevel = level.ToString(),
                        Message = message,
                        Exception = error?.ToString(),
                        CreateTime = DateTime.Now
                    };

                    await _logDAL.AddAsync(log);
                }
                catch (Exception ex)
                {
                    // 如果数据库写入失败，至少确保写入文件日志
                    await LoggerHelper<SysLogService>.ErrorAsync("日志写入失败", ex);
                }
            });
        }

        /// <summary>
        /// 记录详细操作日志（同步方法）
        /// </summary>
        /// <param name="module">模块名称</param>
        /// <param name="description">操作描述</param>
        /// <param name="detailedMessage">详细日志内容</param>
        /// <param name="userId">用户ID</param>
        /// <param name="username">用户名</param>
        /// <param name="params">请求参数</param>
        /// <param name="result">操作结果</param>
        /// <param name="level">日志级别</param>
        /// <param name="ip">IP地址</param>
        /// <param name="path">请求路径</param>
        /// <param name="time">执行时间(毫秒)</param>
        /// <param name="logType">日志类型</param>
        /// <returns>日志ID</returns>
        public async Task<string> LogDetailedAsync(string module, string description, string detailedMessage,
            string? userId = null, string? username = null, object? @params = null, object? result = null,
            LogLevel level = LogLevel.Information, string? ip = null, string? path = null, long time = 0, string logType = "User")
        {
            try
            {
                // 构建日志消息
                var message = $"模块：{module}，操作：{description}，详情：{detailedMessage}";
                if (@params != null)
                    message += $"，参数：{JsonSerializer.Serialize(@params, _indentedJsonOptions)}";
                if (result != null)
                    message += $"，结果：{JsonSerializer.Serialize(result, _indentedJsonOptions)}";

                // 写入文件日志
                switch (level)
                {
                    case LogLevel.Error:
                        await LoggerHelper<SysLogService>.ErrorAsync(message);
                        break;
                    case LogLevel.Warning:
                        await LoggerHelper<SysLogService>.WarnAsync(message);
                        break;
                    default:
                        await LoggerHelper<SysLogService>.InfoAsync(message);
                        break;
                }

                // 写入数据库
                var log = new SysLog
                {
                    Id = Guid.NewGuid().ToString("N"),
                    UserId = userId,
                    Username = username,
                    Operation = description,
                    Method = module,
                    Params = @params != null ? JsonSerializer.Serialize(@params) : null,
                    Time = time,
                    Ip = ip ?? "",
                    Path = path ?? "",
                    LogType = logType,
                    LogLevel = level.ToString(),
                    Message = message,
                    CreateTime = DateTime.Now
                };

                await _logDAL.AddAsync(log);
                return log.Id;
            }
            catch (Exception ex)
            {
                // 如果数据库写入失败，至少确保写入文件日志
                await LoggerHelper<SysLogService>.ErrorAsync("详细日志写入失败", ex);
                return string.Empty;
            }
        }

        /// <summary>
        /// 记录业务操作详细日志
        /// </summary>
        /// <param name="module">模块名称</param>
        /// <param name="operation">操作类型</param>
        /// <param name="businessObject">业务对象名称</param>
        /// <param name="objectId">对象ID</param>
        /// <param name="detailedInfo">详细信息</param>
        /// <param name="beforeData">操作前数据</param>
        /// <param name="afterData">操作后数据</param>
        /// <param name="userId">用户ID</param>
        /// <param name="username">用户名</param>
        /// <param name="level">日志级别</param>
        /// <param name="ip">IP地址</param>
        /// <param name="path">请求路径</param>
        /// <returns>日志ID</returns>
        public async Task<string> LogBusinessOperationAsync(
            string module, string operation, string businessObject, string objectId, string detailedInfo,
            object? beforeData = null, object? afterData = null, string? userId = null, string? username = null,
            LogLevel level = LogLevel.Information, string? ip = null, string? path = null)
        {
            try
            {
                // 构建详细的业务操作日志消息
                var message = $"模块：{module}，操作：{operation}，对象：{businessObject}，ID：{objectId}，详情：{detailedInfo}";

                // 构建参数对象
                var paramsObj = new
                {
                    BusinessObject = businessObject,
                    ObjectId = objectId,
                    BeforeData = beforeData,
                    AfterData = afterData,
                    DetailedInfo = detailedInfo
                };

                // 写入文件日志
                switch (level)
                {
                    case LogLevel.Error:
                        await LoggerHelper<SysLogService>.ErrorAsync(message);
                        break;
                    case LogLevel.Warning:
                        await LoggerHelper<SysLogService>.WarnAsync(message);
                        break;
                    default:
                        await LoggerHelper<SysLogService>.InfoAsync(message);
                        break;
                }

                // 写入数据库
                var log = new SysLog
                {
                    Id = Guid.NewGuid().ToString("N"),
                    UserId = userId,
                    Username = username,
                    Operation = operation,
                    Method = module,
                    Params = JsonSerializer.Serialize(paramsObj, _businessJsonOptions),
                    Time = 0,
                    Ip = ip ?? "",
                    Path = path ?? "",
                    LogType = "Business",
                    LogLevel = level.ToString(),
                    Message = message,
                    CreateTime = DateTime.Now
                };

                await _logDAL.AddAsync(log);
                return log.Id;
            }
            catch (Exception ex)
            {
                // 如果数据库写入失败，至少确保写入文件日志
                await LoggerHelper<SysLogService>.ErrorAsync("业务操作日志写入失败", ex);
                return string.Empty;
            }
        }

        /// <summary>
        /// 使用DTO记录业务操作日志
        /// </summary>
        /// <param name="logDto">业务日志DTO</param>
        /// <returns>日志ID</returns>
        public async Task<string> LogBusinessOperationAsync(BusinessLogDto logDto)
        {
            try
            {
                // 构建详细的业务操作日志消息
                var message = $"模块：{logDto.Module}，操作：{logDto.Operation}，对象：{logDto.BusinessObject}，ID：{logDto.ObjectId}，详情：{logDto.DetailedInfo}";

                // 构建参数对象
                var paramsObj = new
                {
                    logDto.BusinessObject,
                    logDto.ObjectId,
                    logDto.BeforeData,
                    logDto.AfterData,
                    logDto.DetailedInfo
                };

                // 写入文件日志
                switch (logDto.Level)
                {
                    case LogLevel.Error:
                        await LoggerHelper<SysLogService>.ErrorAsync(message);
                        break;
                    case LogLevel.Warning:
                        await LoggerHelper<SysLogService>.WarnAsync(message);
                        break;
                    default:
                        await LoggerHelper<SysLogService>.InfoAsync(message);
                        break;
                }

                // 写入数据库
                var log = new SysLog
                {
                    Id = Guid.NewGuid().ToString("N"),
                    UserId = logDto.UserId,
                    Username = logDto.Username,
                    Operation = logDto.Operation,
                    Method = logDto.Module,
                    Params = JsonSerializer.Serialize(paramsObj, _businessJsonOptions),
                    Time = 0,
                    Ip = logDto.Ip ?? "",
                    Path = logDto.Path ?? "",
                    LogType = "Business",
                    LogLevel = logDto.Level.ToString(),
                    Message = message,
                    CreateTime = DateTime.Now
                };

                await _logDAL.AddAsync(log);
                return log.Id;
            }
            catch (Exception ex)
            {
                // 如果数据库写入失败，至少确保写入文件日志
                await LoggerHelper<SysLogService>.ErrorAsync("业务操作日志写入失败", ex);
                return string.Empty;
            }
        }

        /// <summary>
        /// 添加日志
        /// </summary>
        /// <param name="log">日志实体</param>
        /// <returns>添加结果</returns>
        public Task<bool> AddAsync(SysLog log)
        => _logDAL.AddAsync(log);

        /// <summary>
        /// 创建日志（兼容旧API）
        /// </summary>
        /// <param name="dto">日志创建DTO</param>
        /// <param name="currentUser">当前用户信息</param>
        /// <returns>创建的日志ID</returns>
        public async Task<string> CreateAsync(CreateLogDto dto)
        {
            // 构建日志实体
            var log = new SysLog
            {
                Id = Guid.NewGuid().ToString("N"),
                UserId = dto.UserId,
                Username = dto.Username,
                Operation = dto.Operation,
                Method = dto.Method,
                Params = dto.Params,
                Time = dto.Time,
                Ip = dto.Ip,
                Path = dto.Path,
                LogType = dto.LogType,
                LogLevel = dto.LogLevel,
                Message = dto.Message,
                Exception = dto.Exception,
                CreateTime = DateTime.Now
            };

            try
            {
                // 直接写入数据库，不创建新线程
                await _logDAL.AddAsync(log);

                // 同时写入文件日志
                LogLevel logLevel = Enum.TryParse<LogLevel>(dto.LogLevel, out var level) ? level : LogLevel.Information;
                Exception? exception = !string.IsNullOrEmpty(dto.Exception) ? new Exception(dto.Exception) : null;

                // 根据日志级别写入相应的日志
                switch (logLevel)
                {
                    case LogLevel.Error:
                        await LoggerHelper<SysLogService>.ErrorAsync(dto.Message, exception);
                        break;
                    case LogLevel.Warning:
                        await LoggerHelper<SysLogService>.WarnAsync(dto.Message, exception);
                        break;
                    default:
                        await LoggerHelper<SysLogService>.InfoAsync(dto.Message, exception);
                        break;
                }
            }
            catch (Exception ex)
            {
                // 至少确保写入文件日志
                await LoggerHelper<SysLogService>.ErrorAsync($"数据库日志写入失败: {ex.Message}", ex);

            }

            return log.Id;
        }

        /// <summary>
        /// 查询日志列表（兼容旧API）
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="operation">操作类型</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="logType">日志类型</param>
        /// <returns>日志列表和总数</returns>
        public async Task<(List<SysLog> Items, int Total)> GetLogsAsync(string? username, string? operation,
            DateTime? startTime, DateTime? endTime, int pageIndex = 1, int pageSize = 10, string? logType = null)
        {
            // 转换为新的查询条件
            var queryable = new SysLogDAL.Queryable
            {
                Username = username,
                Operation = operation,
                StartTime = startTime,
                EndTime = endTime,
                PageIndex = pageIndex,
                PageSize = pageSize,
                LogType = logType,
                OrderByCreateTime = 1 // 按创建时间降序排序
            };

            // 使用新的查询方法
            var result = await _logDAL.GetPageAsync(queryable);

            // 返回兼容格式
            return (result.List ?? [], result.TotalCount);
        }

        /// <summary>
        /// 查询日志列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public Task<PageEntity<SysLog>> GetPageAsync(SysLogDAL.Queryable queryable)
        => _logDAL.GetPageAsync(queryable);


        /// <summary>
        /// 获取日志列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>日志列表</returns>
        public Task<List<SysLog>> GetListAsync(SysLogDAL.Queryable queryable)
        => _logDAL.GetListAsync(queryable);


        /// <summary>
        /// 清理指定日期之前的日志
        /// </summary>
        /// <param name="beforeDate">截止日期</param>
        /// <param name="logType">日志类型</param>
        /// <returns>操作结果</returns>
        public Task<bool> ClearLogsAsync(DateTime beforeDate, string? logType = null)
        => _logDAL.ClearLogsAsync(beforeDate, logType);

    }
}