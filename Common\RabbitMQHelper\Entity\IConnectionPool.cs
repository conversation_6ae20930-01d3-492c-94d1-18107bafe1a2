using RabbitMQ.Client;

namespace Common.RabbitMQHelper.Entity;

/// <summary>
/// RabbitMQ 连接池接口
/// </summary>
public interface IConnectionPool : IDisposable
{
    /// <summary>
    /// 获取连接
    /// </summary>
    IConnection GetConnection();

    /// <summary>
    /// 获取通道
    /// </summary>
    IModel GetChannel();

    /// <summary>
    /// 释放通道
    /// </summary>
    void ReleaseChannel(IModel channel);
}