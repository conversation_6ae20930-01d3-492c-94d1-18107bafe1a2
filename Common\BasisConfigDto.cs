﻿namespace Common
{

    public class BasisConfigDto
    {
        /// <summary>
        /// Redis 配置
        /// </summary>
        public static RedisSetting Redis { get; set; } = new RedisSetting();

        public static JWTSetting Jwt { get; set; } = new JWTSetting();

        /// <summary>
        /// 微信配置
        /// </summary>
        public static WxSetting Wx { get; set; } = new WxSetting();



        public static RabbitMQSetting RabbitMQ { get; set; } = new RabbitMQSetting();

        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        public static DataBaseConnectionStrings DataBaseConnectionStrings { get; set; } = new DataBaseConnectionStrings();

        /// <summary>
        /// 日志服务
        /// </summary>
        public static LogServiceSetting LogService { get; set; } = new LogServiceSetting();


        /// <summary>
        /// 外部游戏服务器配置
        /// </summary>
        public static ExternalGameServerSetting ExternalGameServer { get; set; } = new ExternalGameServerSetting();

        /// <summary>
        /// IP白名单配置
        /// </summary>
        public static IPWhiteListSetting IPWhiteList { get; set; } = new IPWhiteListSetting();

        /// <summary>
        /// cardpoen 开放接口配置
        /// </summary>
        public static CardpoenApiSetting CardpoenApi { get; set; } = new CardpoenApiSetting();

    }


    /// <summary>
    /// cardpoen 开放接口配置
    /// </summary>
    public class CardpoenApiSetting
    {
        /// <summary>
        /// 开放接口密钥
        /// </summary>
        public static string OpenApiKey { get; set; } = string.Empty;
    }

    /// <summary>
    /// 外部游戏服务器配置
    /// </summary>
    public class ExternalGameServerSetting
    {
        /// <summary>
        /// Base64 字典
        /// </summary>
        public string Base64Dict { get; set; } = string.Empty;

        /// <summary>
        /// 默认 API 地址
        /// </summary>
        public string DefaultApiUrl { get; set; } = string.Empty;

        /// <summary>
        /// 默认加密密钥
        /// </summary>
        public string DefaultEncryptKey { get; set; } = string.Empty;
    }


    /// <summary>
    /// 日志服务配置
    /// </summary>
    public class LogServiceSetting
    {

        public static string Path { get; set; } = string.Empty;

    }


    /// <summary>
    /// 数据库连接字符串
    /// </summary>
    public class DataBaseConnectionStrings
    {
        /// <summary>
        /// Mysql 连接字符串
        /// </summary>
        public static string MysqlConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// Sqlserver 连接字符串
        /// </summary>
        public static string SqlServerConnectionString { get; set; } = string.Empty;

    }
    /// <summary>
    /// Redis配置
    /// </summary>
    public class RabbitMQSetting
    {
        /// <summary>
        /// 用户名称
        /// </summary>
        public static string? UserName { get; set; }
        /// <summary>
        /// 主机名称
        /// </summary>
        public static string? HostName { get; set; }
        /// <summary>
        /// 端口
        /// </summary>
        public static string? Port { get; set; }
        /// <summary>
        /// 密码
        /// </summary>
        public static string? Password { get; set; }
        /// <summary>
        /// 虚拟主机
        /// </summary>
        public static string? VirtualHost { get; set; }
    }



    /// <summary>
    /// Redis 配置
    /// </summary>
    public class RedisSetting
    {
        /// <summary>
        /// Redis实例列表
        /// </summary>
        public static List<RedisInstance> Instances { get; set; } = [];

        /// <summary>
        /// 获取默认实例（第一个实例）
        /// </summary>
        public static RedisInstance DefaultInstance
        {
            get
            {
                if (Instances.Count == 0)
                {
                    throw new InvalidOperationException("没有配置Redis实例");
                }
                return Instances[0];
            }
        }

        /// <summary>
        /// 根据实例名称获取Redis实例
        /// </summary>
        /// <param name="instanceName">实例名称</param>
        /// <returns>Redis实例，如果未找到则返回默认实例</returns>
        public static RedisInstance GetInstance(string instanceName)
        {
            return Instances.FirstOrDefault(x => x.InstanceName == instanceName) ?? DefaultInstance;
        }
    }

    /// <summary>
    /// Redis实例配置
    /// </summary>
    public class RedisInstance
    {
        /// <summary>
        /// 链接信息 地址，端口号，密码
        /// </summary>
        public string Connection { get; set; } = string.Empty;

        /// <summary>
        /// 默认数据库
        /// </summary>
        public int DefaultDb { get; set; }

        /// <summary>
        /// 实例名
        /// </summary>
        public string InstanceName { get; set; } = string.Empty;
    }

    public class JWTSetting
    {
        /// <summary>
        /// JWT 加密密钥
        /// </summary>
        public static string Sign { get; set; } = "";

        /// <summary>
        /// 过期时间
        /// </summary>
        public static int Exp { get; set; }

        /// <summary>
        /// 发行者
        /// </summary>
        public static string Issuer { get; set; } = "";

        /// <summary>
        /// 受众
        /// </summary>
        public static string Audience { get; set; } = "";
    }


    public class WxSetting
    {
        /// <summary>
        /// 微信App ID
        /// </summary>
        public static string AppId { get; set; } = "";

        /// <summary>
        /// 过期时间
        /// </summary>
        public static string AppSecret { get; set; } = "";
    }

    /// <summary>
    /// IP白名单配置
    /// </summary>
    public class IPWhiteListSetting
    {
        /// <summary>
        /// 允许访问的IP列表
        /// </summary>
        public static List<string> AllowedIPs { get; set; } = [];
    }
}
