using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.SysEntity
{
    /// <summary>
    /// 系统日志表
    /// </summary>
    [Table("sys_log")]
    public class SysLog
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        [Key]
        [Comment("日志ID")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        [Comment("用户ID")]
        public string? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        [Comment("用户名")]
        public string? Username { get; set; }

        /// <summary>
        /// 操作类型（如：登录、新增、修改、删除等）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        [Comment("操作类型")]
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// 请求方法
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        [Comment("请求方法")]
        public string Method { get; set; } = string.Empty;

        /// <summary>
        /// 请求参数
        /// </summary>
        [Comment("请求参数")]
        public string? Params { get; set; }

        /// <summary>
        /// 执行时长(毫秒)
        /// </summary>
        [Column(TypeName = "bigint")]
        [Comment("执行时长(毫秒)")]
        public long Time { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        [Comment("IP地址")]
        public string Ip { get; set; } = string.Empty;

        /// <summary>
        /// 请求路径
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        [Comment("请求路径")]
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 日志类型（User=用户操作日志，System=系统日志）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        [Comment("日志类型")]
        public string LogType { get; set; } = "System";

        /// <summary>
        /// 日志级别（INFO、ERROR等）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        [Comment("日志级别")]
        public string LogLevel { get; set; } = string.Empty;

        /// <summary>
        /// 日志内容
        /// </summary>
        [Comment("日志内容")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 异常信息
        /// </summary>
        [Comment("异常信息")]
        public string? Exception { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Comment("创建时间")]
        public DateTime CreateTime { get; set; } = DateTime.Now;
    }
}