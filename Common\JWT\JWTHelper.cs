﻿using Common.Exceptions;
using Common.Log4Net;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace Common.JWT
{
    /// <summary>
    /// 用户信息模型
    /// </summary>
    public class UserInfo
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 是否为管理员
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 角色列表
        /// </summary>
        public List<string> Roles { get; set; } = [];
    }

    /// <summary>
    /// JWT帮助类，用于生成和验证JWT令牌
    /// </summary>
    public class JWTHelper
    {
        /// <summary>
        /// 生成秘钥
        /// </summary>
        private readonly static SymmetricSecurityKey _sign = new(Encoding.UTF8.GetBytes(JWTSetting.Sign));

        /// <summary>
        /// 创建JWT令牌
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        /// <returns>JWT令牌字符串</returns>
        public static string CreateJwt(UserInfo userInfo)
        {
            // 创建声明
            var claims = new List<Claim>
            {
                new (ClaimTypes.NameIdentifier, userInfo.UserId),
                new (ClaimTypes.Name, userInfo.UserName)
            };

            // 添加管理员角色声明
            if (userInfo.IsAdmin)
            {
                claims.Add(new Claim(ClaimTypes.Role, "Admin"));
            }

            // 添加其他角色声明
            foreach (var role in userInfo.Roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            return CreateJwt(claims);
        }

        /// <summary>
        /// 创建JWT令牌
        /// </summary>
        /// <param name="claims">声明集合</param>
        /// <returns>JWT令牌字符串</returns>
        public static string CreateJwt(List<Claim> claims)
        {
            // 获取秘钥
            var credentials = new SigningCredentials(_sign, SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                issuer: JWTSetting.Issuer,
                audience: JWTSetting.Audience,
                claims: claims,
                expires: DateTime.Now.AddSeconds(JWTSetting.Exp),
                signingCredentials: credentials
            );
            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        /// <summary>
        /// 构建Token验证参数
        /// </summary>
        /// <returns>验证参数</returns>
        private static TokenValidationParameters GetValidationParameters()
        {
            return new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = _sign,
                ValidateIssuer = true,
                ValidIssuer = JWTSetting.Issuer,
                ValidateAudience = true,
                ValidAudience = JWTSetting.Audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero,
                RequireExpirationTime = true
            };
        }

        /// <summary>
        /// 解析并验证Token
        /// </summary>
        /// <param name="token">JWT令牌</param>
        /// <returns>身份验证主体</returns>
        /// <exception cref="AuthorizationException">当令牌验证失败时抛出</exception>
        private static ClaimsPrincipal ValidateToken(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                throw new AuthorizationException("令牌不能为空");
            }

            // 移除Bearer前缀
            token = token.Replace("Bearer ", "", StringComparison.OrdinalIgnoreCase).Trim();

            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var principal = tokenHandler.ValidateToken(token, GetValidationParameters(), out SecurityToken validatedToken);

                // 确保token类型正确
                if (validatedToken is not JwtSecurityToken jwtSecurityToken ||
                    !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                {
                    throw new SecurityTokenException("无效的令牌");
                }

                return principal;
            }
            catch (SecurityTokenExpiredException)
            {
                //_ = LoggerHelper<JWTHelper>.InfoAsync("令牌已过期");
                throw new AuthorizationException("令牌已过期");
            }
            catch (SecurityTokenInvalidSignatureException)
            {
                //_ = LoggerHelper<JWTHelper>.InfoAsync("令牌签名无效");
                throw new AuthorizationException("令牌签名无效");
            }
            catch (SecurityTokenValidationException ex)
            {
                _ = LoggerHelper<JWTHelper>.InfoAsync("令牌验证失败", ex);
                throw new AuthorizationException("令牌验证失败");
            }
            catch (Exception ex)
            {
                _ = LoggerHelper<JWTHelper>.InfoAsync("令牌处理过程中发生错误", ex);
                throw new AuthorizationException("令牌验证失败");
            }
        }

        /// <summary>
        /// 校验解析token
        /// </summary>
        /// <param name="token">JWT令牌字符串</param>
        /// <returns>解析后的payload字符串</returns>
        /// <exception cref="AuthorizationException">当token验证失败时抛出</exception>
        public static string ValidateJwtToken(string token)
        {
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadJwtToken(token.Replace("Bearer ", "", StringComparison.OrdinalIgnoreCase).Trim());
            return JsonConvert.SerializeObject(jwtToken.Payload);
        }

        /// <summary>
        /// 从HttpContext中获取JWT令牌
        /// </summary>
        /// <param name="httpContext">HTTP上下文</param>
        /// <returns>令牌字符串，未找到返回空字符串</returns>
        public static string GetToken(HttpContext httpContext)
        {
            // 获取token
            if (!httpContext.Request.Headers.TryGetValue("Authorization", out var authHeader))
                return "";

            var token = authHeader.ToString().Replace("Bearer ", "");
            if (string.IsNullOrEmpty(token))
                return "";

            return token;
        }

        /// <summary>
        /// 从JWT令牌中获取用户信息
        /// </summary>
        /// <param name="token">JWT令牌，格式可以是"Bearer token"或纯token</param>
        /// <returns>用户信息</returns>
        /// <exception cref="AuthorizationException">当token验证失败时抛出</exception>
        public static UserInfo GetUserInfo(string token)
        {
            var principal = ValidateToken(token);

            // 获取用户ID
            var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || string.IsNullOrEmpty(userIdClaim.Value))
            {
                throw new AuthorizationException("未找到有效的用户ID");
            }

            // 获取用户名
            var userNameClaim = principal.FindFirst(ClaimTypes.Name);
            if (userNameClaim == null || string.IsNullOrEmpty(userNameClaim.Value))
            {
                throw new AuthorizationException("未找到有效的用户名");
            }

            // 检查是否是管理员账号
            bool isAdmin = principal.HasClaim(ClaimTypes.Name, "admin");

            // 获取所有角色
            var roles = principal.FindAll(ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();

            return new UserInfo
            {
                UserId = userIdClaim.Value,
                UserName = userNameClaim.Value,
                IsAdmin = isAdmin,
                Roles = roles
            };
        }
    }
}
