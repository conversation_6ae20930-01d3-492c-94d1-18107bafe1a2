using System.Security.Cryptography;
using System.Text;

namespace Common.Helper
{
    public static class EncryptHelper
    {
        /// <summary>
        /// 使用MD5加密密码
        /// </summary>
        /// <param name="password">明文密码</param>
        /// <returns>MD5加密后的密码</returns>
        public static string EncryptPassword(string password)
        {
            byte[] inputBytes = Encoding.UTF8.GetBytes(password);
            byte[] hashBytes = MD5.HashData(inputBytes);

            StringBuilder sb = new();
            for (int i = 0; i < hashBytes.Length; i++)
            {
                sb.Append(hashBytes[i].ToString("x2"));
            }
            return sb.ToString();
        }


    }
}