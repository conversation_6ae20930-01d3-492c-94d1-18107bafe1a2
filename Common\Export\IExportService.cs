using Microsoft.AspNetCore.Mvc;

namespace Common.Export
{
    /// <summary>
    /// 导出服务接口
    /// </summary>
    public interface IExportService
    {
        /// <summary>
        /// 导出CSV文件
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="data">要导出的数据</param>
        /// <param name="columns">要导出的列配置</param>
        /// <returns>文件字节数组</returns>
        byte[] ExportToCsv<T>(IEnumerable<T> data, List<ExportColumn> columns);

        /// <summary>
        /// 通用导出方法
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="data">要导出的数据</param>
        /// <param name="exportRequest">导出配置</param>
        /// <param name="defaultFileName">默认文件名</param>
        /// <returns>文件结果</returns>
        FileContentResult ExportToFile<T>(IEnumerable<T> data, ExportRequest exportRequest, string defaultFileName);
    }
}