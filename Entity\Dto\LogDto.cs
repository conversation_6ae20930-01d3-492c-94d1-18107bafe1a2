using System.ComponentModel.DataAnnotations;

namespace Entity.Dto
{
    /// <summary>
    /// 日志查询参数DTO
    /// </summary>
    public class LogQueryDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string? Operation { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 页码，默认为1
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页大小，默认为10
        /// </summary>
        [Range(1, 100, ErrorMessage = "每页大小必须在1-100之间")]
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 日志类型（User=用户操作日志，System=系统日志）
        /// </summary>
        public string? LogType { get; set; }
    }

    /// <summary>
    /// 清理日志参数DTO
    /// </summary>
    public class LogClearDto
    {
        /// <summary>
        /// 清理指定日期之前的日志
        /// </summary>
        [Required(ErrorMessage = "清理日期不能为空")]
        public DateTime BeforeDate { get; set; }

        /// <summary>
        /// 日志类型（User=用户操作日志，System=系统日志）
        /// </summary>
        public string? LogType { get; set; }
    }

    /// <summary>
    /// 日志详情DTO
    /// </summary>
    public class LogDetailDto
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// 请求方法
        /// </summary>
        public string Method { get; set; } = string.Empty;

        /// <summary>
        /// 请求参数
        /// </summary>
        public string? Params { get; set; }

        /// <summary>
        /// 执行时长(毫秒)
        /// </summary>
        public long Time { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string Ip { get; set; } = string.Empty;

        /// <summary>
        /// 请求路径
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 日志类型
        /// </summary>
        public string LogType { get; set; } = string.Empty;

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = string.Empty;

        /// <summary>
        /// 日志内容
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 异常信息
        /// </summary>
        public string? Exception { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 创建日志的数据传输对象
    /// </summary>
    public class CreateLogDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// 请求方法
        /// </summary>
        public string Method { get; set; } = string.Empty;

        /// <summary>
        /// 请求参数
        /// </summary>
        public string? Params { get; set; }

        /// <summary>
        /// 执行时长(毫秒)
        /// </summary>
        public long Time { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string Ip { get; set; } = string.Empty;

        /// <summary>
        /// 请求路径
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 日志类型
        /// </summary>
        public string LogType { get; set; } = "System";

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = string.Empty;

        /// <summary>
        /// 日志内容
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 异常信息
        /// </summary>
        public string? Exception { get; set; }
    }
}