using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.OrganizationDto
{
    /// <summary>
    /// 权限DTO
    /// </summary>
    public class PermissionDto
    {
        [MaxLength(32)]
        public string Id { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public byte Type { get; set; }
        public int Status { get; set; }
        public string? Description { get; set; }
        [MaxLength(32)]
        public string? ParentId { get; set; }
        public string? ParentName { get; set; }
        public int Sort { get; set; }
        [MaxLength(32)]
        public string? MenuId { get; set; }
        public string? Remark { get; set; }
        public List<PermissionDto> Children { get; set; } = [];
    }

    /// <summary>
    /// 权限分配DTO
    /// </summary>
    public class PermissionAssignmentDto
    {
        [MaxLength(32)]
        public string Id { get; set; } = string.Empty;
        public int TargetType { get; set; }
        [MaxLength(32)]
        public string TargetId { get; set; } = string.Empty;
        [MaxLength(32)]
        public string PermissionId { get; set; } = string.Empty;
        public string PermissionName { get; set; } = string.Empty;
        public string PermissionCode { get; set; } = string.Empty;
        public int GrantType { get; set; }
        public int Priority { get; set; }
    }

    /// <summary>
    /// 分配权限DTO
    /// </summary>
    public class AssignPermissionDto
    {
        [Required]
        public int TargetType { get; set; }

        [Required]
        [MaxLength(32)]
        public string TargetId { get; set; } = string.Empty;

        [Required]
        [MaxLength(32)]
        public string PermissionId { get; set; } = string.Empty;

        public int GrantType { get; set; }

        public int Priority { get; set; }
    }
}