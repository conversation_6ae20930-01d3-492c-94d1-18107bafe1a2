using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.SysEntity
{
    /// <summary>
    /// 系统字典表
    /// </summary>
    [Table("sys_dictionary")]
    public class SysDictionary : BaseEntity_GUID
    {
        /// <summary>
        /// 字典类型码
        /// </summary>
        [MaxLength(50)]
        public string DictTypeCode { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型名称
        /// </summary>
        [MaxLength(100)]
        public string DictTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 字典项编码
        /// </summary>
        [MaxLength(50)]
        public string DictItemCode { get; set; } = string.Empty;

        /// <summary>
        /// 字典项名称
        /// </summary>
        [MaxLength(100)]
        public string DictItemName { get; set; } = string.Empty;

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否启用(1:启用 0:禁用)
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 父级ID
        /// </summary>
        [MaxLength(32)]
        public string? ParentId { get; set; }

        /// <summary>
        /// 扩展字段1
        /// </summary>
        [MaxLength(500)]
        public string? ExtendField1 { get; set; }

        /// <summary>
        /// 扩展字段2
        /// </summary>
        [MaxLength(500)]
        public string? ExtendField2 { get; set; }
    }
}