﻿using Entity.Entitys;
using Entity.Entitys.CardEntity;
using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;

namespace DAL
{
    /// <summary>
    /// 数据库上下文类,继承自DbContext
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="options">数据库上下文配置选项</param>
    public class MyContext(DbContextOptions<MyContext> options) : DbContext(options)
    {
        #region DbSet属性

        #region 系统相关表

        /// <summary>
        /// 系统权限表
        /// </summary>
        public DbSet<SysPermission> SysPermissions { get; set; }

        /// <summary>
        /// 系统用户表
        /// </summary>
        public DbSet<SysUser> SysUsers { get; set; }

        /// <summary>
        /// 系统角色表
        /// </summary>
        public DbSet<SysRole> SysRoles { get; set; }

        /// <summary>
        /// 系统菜单表
        /// </summary>
        public DbSet<SysMenu> SysMenus { get; set; }

        /// <summary>
        /// 用户角色关联表
        /// </summary>
        public DbSet<SysUserRole> SysUserRoles { get; set; }


        /// <summary>
        /// 系统日志表
        /// </summary>
        public DbSet<SysLog> SysLogs { get; set; }

        /// <summary>
        /// 系统字典表
        /// </summary>
        public DbSet<SysDictionary> SysDictionaries { get; set; }

        /// <summary>
        /// 系统按钮表
        /// </summary>
        public DbSet<SysButton> SysButtons { get; set; }

        /// <summary>
        /// 短信验证码表
        /// </summary>
        public DbSet<SmsVerificationCode> SmsVerificationCodes { get; set; }

        #endregion

        #region 卡券相关表

        /// <summary>
        /// 卡券信息表
        /// </summary>
        public DbSet<CardInfo> CardInfos { get; set; }

        /// <summary>
        /// 卡券批次表
        /// </summary>
        public DbSet<CardBatch> CardBatches { get; set; }

        ///// <summary>
        ///// 卡券使用记录表
        ///// </summary>
        //public DbSet<CardUseRecord> CardUseRecords { get; set; }

        /// <summary>
        /// 卡券操作记录表
        /// </summary>
        public DbSet<CardOperationRecord> CardOperationRecords { get; set; }

        /// <summary>
        /// 渠道卡券价值配置表
        /// </summary>
        public DbSet<CardValueConfig> ChannelCardValueConfigs { get; set; }

        /// <summary>
        /// 发货目标表
        /// </summary>
        public DbSet<CardShippingTarget> ShippingTargets { get; set; }

        /// <summary>
        /// 发货记录表
        /// </summary>
        public DbSet<CardShippingRecord> ShippingRecords { get; set; }



        #endregion
        #endregion
    }
}
