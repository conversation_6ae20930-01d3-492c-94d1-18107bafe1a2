using Entity.Dto;
using Entity.Entitys;

namespace Entity.Extensions
{
    /// <summary>
    /// 实体扩展方法
    /// </summary>
    public static class EntityExtensions
    {
        /// <summary>
        /// 初始化实体，用于添加操作
        /// 自动设置ID、创建时间等基础字段
        /// </summary>
        /// <param name="entity">要初始化的实体</param>
        /// <param name="createdBy">创建人ID（可选）</param>
        public static void InitializeForAdd(this BaseEntity_GUID entity, CurrentUserInfoDto createdBy)
        {
            entity.Id = Guid.NewGuid().ToString("N");
            entity.CreateTime = DateTime.Now;
            entity.CreatedBy = createdBy.UserId;
            entity.CreatorName = createdBy.UserName;
        }

        /// <summary>
        /// 初始化实体，用于添加操作
        /// 自动设置ID、创建时间等基础字段
        /// </summary>
        /// <param name="entity">要初始化的实体</param>
        /// <param name="createdBy">创建人ID（可选）</param>
        public static void InitializeForAdd(this BaseEntity entity, CurrentUserInfoDto createdBy)
        {
            entity.CreateTime = DateTime.Now;
            entity.CreatedBy = createdBy.UserId;
            entity.CreatorName = createdBy.UserName;
        }

        /// <summary>
        /// 初始化实体，用于添加操作
        /// 自动设置ID、创建时间等基础字段
        /// </summary>
        /// <param name="entity">要初始化的实体</param>
        /// <param name="createdBy">创建人ID（可选）</param>
        public static void InitializeForAdd(this BaseEntity_ID entity, CurrentUserInfoDto createdBy)
        {
            entity.CreateTime = DateTime.Now;
            entity.CreatedBy = createdBy.UserId;
            entity.CreatorName = createdBy.UserName;
        }

        /// <summary>
        /// 初始化实体，用于修改操作
        /// 自动设置更新时间等基础字段
        /// </summary>
        /// <param name="entity">要初始化的实体</param>
        /// <param name="updatedBy">更新人ID（可选）</param>
        public static void InitializeForUpdate(this BaseEntity_GUID entity, CurrentUserInfoDto updatedBy)
        {
            entity.UpdateTime = DateTime.Now;
            entity.UpdatedBy = updatedBy.UserId;
            entity.UpdaterName = updatedBy.UserName;
        }

        /// <summary>
        /// 初始化实体，用于修改操作
        /// 自动设置更新时间等基础字段
        /// </summary>
        /// <param name="entity">要初始化的实体</param>
        /// <param name="updatedBy">更新人ID（可选）</param>
        public static void InitializeForUpdate(this BaseEntity_ID entity, CurrentUserInfoDto updatedBy)
        {
            entity.UpdateTime = DateTime.Now;
            entity.UpdatedBy = updatedBy.UserId;
            entity.UpdaterName = updatedBy.UserName;
        }

        /// <summary>
        /// 初始化实体，用于修改操作
        /// 自动设置更新时间等基础字段
        /// </summary>
        /// <param name="entity">要初始化的实体</param>
        /// <param name="updatedBy">更新人ID（可选）</param>
        public static void InitializeForUpdate(this BaseEntity entity, CurrentUserInfoDto updatedBy)
        {
            entity.UpdateTime = DateTime.Now;
            entity.UpdatedBy = updatedBy.UserId;
            entity.UpdaterName = updatedBy.UserName;
        }
    }
}