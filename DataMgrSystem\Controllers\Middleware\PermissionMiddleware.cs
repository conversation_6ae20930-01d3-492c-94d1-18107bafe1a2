using BLL.SysService;
using Common;
using Common.Helper;
using Common.JWT;
using Common.Redis;
using DataMgrSystem.Controllers.Attributes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using Newtonsoft.Json;
using System.Reflection;
using System.Security.Claims;

namespace DataMgrSystem.Controllers.Middleware
{
    /// <summary>
    /// 权限中间件
    /// 用于验证用户权限和身份认证，确保只有授权用户能访问受保护的API端点
    /// </summary>
    /// <param name="next">请求处理管道中的下一个中间件</param>
    /// <param name="permissionService">权限服务</param>
    public class PermissionMiddleware(
        RequestDelegate next,
        SysPermissionService permissionService)
    {
        /// <summary>
        /// 请求处理管道中的下一个中间件
        /// </summary>
        private readonly RequestDelegate _next = next;

        /// <summary>
        /// 权限服务，用于获取和验证用户权限
        /// </summary>
        private readonly SysPermissionService _permissionService = permissionService;

        /// <summary>
        /// 用户登录状态在Redis中的键前缀
        /// </summary>
        private const string USER_LOGIN_STATUS_PREFIX = "UserLoginStatus:";

        /// <summary>
        /// 中间件处理方法，验证用户权限并处理请求
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>异步任务</returns>
        public async Task InvokeAsync(HttpContext context)
        {
            // 获取当前请求的终结点
            var endpoint = context.GetEndpoint();

            // 如果终结点为空或允许匿名访问，则直接放行
            if (endpoint == null || IsAnonymousEndpoint(endpoint))
            {
                await _next(context);
                return;
            }

            // 获取终结点上的特性
            var endpointAttributes = GetEndpointAttributes(endpoint);

            try
            {
                // 验证身份和权限
                if (endpointAttributes.PermissionAttribute != null && !await ValidatePermissionAsync(context, endpointAttributes))
                    return;

                // 验证开放接口
                if (endpointAttributes.CardOpenValidationAttribute != null && !await ValidateOpenPermissionAsync(context))
                    return;

                await _next(context);
            }
            catch (Exception)
            {
                throw;
            }
        }

        #region 开放接口验证
        /// <summary>
        /// 开放接口验证
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>验证是否成功</returns>
        private static async Task<bool> ValidateOpenPermissionAsync(HttpContext context)
        {
            var (isValid, errorMessage, obj) = await SignatureValidationHelper.ValidateFromHttpContext(context, CardpoenApiSetting.OpenApiKey);
            if (!isValid)
                return await WriteErrorResponse(context, 500, errorMessage);


            //使用缓存的PropertyInfo提高反射性能
            if (obj != null)
            {
                var playerToken = obj.FirstOrDefault(q => q.Key.Equals("PlayerToken", StringComparison.CurrentCultureIgnoreCase)).Value.ToString();
                var playerId = obj.FirstOrDefault(q => q.Key.Equals("PlayerId", StringComparison.CurrentCultureIgnoreCase)).Value.ToString();

                // 验证玩家Token
                if (string.IsNullOrEmpty(playerToken))
                    return await WriteErrorResponse(context, 500, "用户Token不能为空");
                // 验证玩家ID
                if (string.IsNullOrEmpty(playerId))
                    return await WriteErrorResponse(context, 500, "玩家ID不能为空");
                // 验证用户Token
                if (!ValidatePlayerToken(playerId, playerToken))
                    return await WriteErrorResponse(context, 500, "用户Token验证失败");
            }
            return true;
        }

        /// <summary>
        /// 尝试获取对象属性值
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="obj">目标对象</param>
        /// <param name="type">对象类型</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="value">输出属性值</param>
        /// <returns>是否成功获取</returns>
        private static bool TryGetPropertyValue<T>(object obj, Type type, string propertyName, out T value)
        {
            PropertyInfo? propertyInfo = type.GetProperty(propertyName);
            if (propertyInfo == null)
            {
                value = default!;
                return false;
            }

            object? rawValue = propertyInfo.GetValue(obj);
            if (rawValue == null)
            {
                value = default!;
                return false;
            }

            if (rawValue is T typedValue)
            {
                value = typedValue;
                return true;
            }

            try
            {
                value = (T)Convert.ChangeType(rawValue, typeof(T));
                return true;
            }
            catch
            {
                value = default!;
                return false;
            }
        }

        /// <summary>
        /// 验证玩家Token
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="playerToken">玩家Token</param>
        /// <returns>验证是否通过</returns>
        private static bool ValidatePlayerToken(string playerId, string playerToken)
        {
            var token = RedisHelper.Get(playerId, "game");

            if (token.IsNullOrEmpty) return false;

            // 字符串比较应使用不变的比较方式，避免区域设置差异
            return string.Equals(token.ToString(), playerToken, StringComparison.Ordinal);
        }
        #endregion

        #region 身份验证和权限验证

        /// <summary>
        /// 身份和权限验证
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="attributes">权限特性</param>
        /// <returns>验证是否成功</returns>        
        private async Task<bool> ValidatePermissionAsync(HttpContext context, EndpointAttributes attributes)
        {
            // 验证用户身份并获取用户信息
            var (userInfo, _) = await ValidateUserAsync(context);
            if (userInfo == null) return false;

            // 设置用户身份信息到HttpContext中
            context.User = CreateClaimsPrincipal(userInfo);

            // 管理员直接放行，不需要验证具体权限
            if (userInfo.IsAdmin) return true;

            // 验证功能权限
            if (attributes.FunctionPermissionAttribute != null)
                if (!await ValidateFunctionPermissionAsync(userInfo.UserId, attributes.FunctionPermissionAttribute))
                {
                    await HandleForbidden(context, $"没有权限访问此功能: {attributes.FunctionPermissionAttribute.Description}");
                    return false;
                }

            return true;
        }

        /// <summary>
        /// 检查终结点是否允许匿名访问
        /// </summary>
        /// <param name="endpoint">HTTP终结点</param>
        /// <returns>如果允许匿名访问则返回true，否则返回false</returns>
        private static bool IsAnonymousEndpoint(Endpoint endpoint) =>
            endpoint.Metadata.GetMetadata<AllowAnonymousAttribute>() != null;

        /// <summary>
        /// 获取终结点上的权限特性
        /// </summary>
        /// <param name="endpoint">HTTP终结点</param>
        /// <returns>包含权限特性和功能权限特性的对象</returns>
        private static EndpointAttributes GetEndpointAttributes(Endpoint endpoint)
        {
            // 获取控制器动作描述符
            var actionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
            if (actionDescriptor == null) return new();

            // 获取方法和控制器上的所有特性
            var methodAttributes = actionDescriptor.MethodInfo.GetCustomAttributes(true);
            var controllerAttributes = actionDescriptor.ControllerTypeInfo.GetCustomAttributes(true);

            // 获取方法或控制器上的权限特性
            var permission = methodAttributes.FirstOrDefault(a => a is PermissionAttribute) as PermissionAttribute
                ?? controllerAttributes.FirstOrDefault(a => a is PermissionAttribute) as PermissionAttribute;

            // 获取方法上的功能权限特性和卡片开通验证特性
            var functionPermission = methodAttributes.FirstOrDefault(a => a is FunctionPermissionAttribute) as FunctionPermissionAttribute
                ?? controllerAttributes.FirstOrDefault(a => a is FunctionPermissionAttribute) as FunctionPermissionAttribute;

            var cardOpenValidation = methodAttributes.FirstOrDefault(a => a is CardOpenValidationAttribute) as CardOpenValidationAttribute
                ?? controllerAttributes.FirstOrDefault(a => a is CardOpenValidationAttribute) as CardOpenValidationAttribute;

            return new EndpointAttributes
            {
                PermissionAttribute = permission,
                FunctionPermissionAttribute = functionPermission,
                CardOpenValidationAttribute = cardOpenValidation
            };
        }

        /// <summary>
        /// 验证用户身份并获取用户信息
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>包含用户信息和令牌的元组</returns>
        private static async Task<(UserInfo? userInfo, string token)> ValidateUserAsync(HttpContext context)
        {
            // 从请求中获取JWT令牌
            var token = JWTHelper.GetToken(context);

            // 从令牌中解析用户信息
            var userInfo = JWTHelper.GetUserInfo(token);
            if (userInfo == null)
            {
                await HandleUnauthorized(context, "token验证失败");
                return (null, token);
            }

            // 验证用户登录状态（防止多设备登录或令牌被盗用）
            string loginStatusKey = $"{USER_LOGIN_STATUS_PREFIX}{userInfo.UserId}";
            string? storedToken = RedisHelper.Get<string>(loginStatusKey);
            if (string.IsNullOrEmpty(storedToken) || storedToken != token)
            {
                await HandleUnauthorized(context, "您的账号已在其他设备登录，请重新登录");
                return (null, token);
            }

            return (userInfo, token);
        }

        /// <summary>
        /// 创建用户身份主体
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        /// <returns>用户身份主体</returns>
        private static ClaimsPrincipal CreateClaimsPrincipal(UserInfo userInfo)
        => new(new ClaimsIdentity(
            [
                new(ClaimTypes.NameIdentifier, userInfo.UserId),
                new(ClaimTypes.Name, userInfo.UserName)
            ], "Bearer"));


        /// <summary>
        /// 验证用户是否具有特定功能权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="attribute">功能权限特性</param>
        /// <returns>如果用户具有权限则返回true，否则返回false</returns>
        private async Task<bool> ValidateFunctionPermissionAsync(string userId, FunctionPermissionAttribute attribute)
        {
            // 获取用户的按钮权限列表
            var userPermissions = await _permissionService.GetButtonPermissionsAsync(userId);

            // 检查用户是否具有特定权限代码
            return userPermissions?.Any(p => p.PermissionCode == attribute.Code) == true;
        }
        #endregion

        /// <summary>
        /// 处理未授权响应（401）
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="message">错误消息</param>
        /// <returns>异步任务</returns>
        private static async Task<bool> HandleUnauthorized(HttpContext context, string message)
            => await WriteErrorResponse(context, 401, message);

        /// <summary>
        /// 处理禁止访问响应（403）
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="message">错误消息</param>
        /// <returns>异步任务</returns>
        private static async Task<bool> HandleForbidden(HttpContext context, string message)
            => await WriteErrorResponse(context, 403, message);

        /// <summary>
        /// 写入HTTP响应
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="code">业务状态码</param>
        /// <param name="message">响应消息</param>
        /// <param name="statusCode">HTTP状态码</param>
        /// <returns>异步任务</returns>
        private static async Task<bool> WriteErrorResponse(HttpContext context, int code, string message, int statusCode = 200)
        {
            // 设置响应状态码和内容类型
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";
            // 将结果序列化为JSON并写入响应
            await context.Response.WriteAsync(JsonConvert.SerializeObject(new { code, success = false, msg = message }));

            return false;
        }

        /// <summary>
        /// 获取终结点上的权限特性
        /// </summary>
        private class EndpointAttributes
        {
            /// <summary>
            /// 权限特性
            /// </summary>
            public PermissionAttribute? PermissionAttribute { get; set; }

            /// <summary>
            /// 功能权限特性
            /// </summary>
            public FunctionPermissionAttribute? FunctionPermissionAttribute { get; set; }

            /// <summary>
            /// 卡片开通验证特性
            /// </summary>
            public CardOpenValidationAttribute? CardOpenValidationAttribute { get; set; }
        }
    }

    /// <summary>
    /// 权限中间件扩展方法
    /// 提供将权限中间件添加到应用程序请求管道的扩展方法
    /// </summary>
    public static class PermissionMiddlewareExtensions
    {
        /// <summary>
        /// 向应用程序请求管道添加权限验证中间件
        /// </summary>
        /// <param name="builder">应用程序构建器</param>
        /// <returns>应用程序构建器</returns>
        public static IApplicationBuilder UsePermissionValidation(this IApplicationBuilder builder) =>
            builder.UseMiddleware<PermissionMiddleware>();
    }
}
