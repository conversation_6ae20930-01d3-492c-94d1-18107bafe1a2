using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.SysEntity
{
    /// <summary>
    /// 系统用户实体类
    /// 用于存储系统用户的基本信息和登录信息
    /// </summary>
    [Table("sys_user")]
    public class SysUser : BaseEntity
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Comment("用户ID")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// 用于登录的唯一标识
        /// </summary>
        [Required]
        [Column(TypeName = "varchar(100)")]
        [Comment("用户名")]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// 用户的登录密码,需要加密存储
        /// </summary>
        [Required]
        [Column(TypeName = "varchar(100)")]
        [Comment("密码")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 真实姓名
        /// 用户的实际姓名
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        [Comment("真实姓名")]
        public string RealName { get; set; } = string.Empty;

        /// <summary>
        /// 头像
        /// 用户头像的URL地址
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        [Comment("头像")]
        public string Avatar { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮箱
        /// 用户的联系邮箱
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        [Comment("电子邮箱")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 手机号码
        /// 用户的联系电话
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        [Comment("手机号码")]
        public string Mobile { get; set; } = string.Empty;

        /// <summary>
        /// 用户状态
        /// 0:禁用 1:正常
        /// </summary>
        [Column(TypeName = "tinyint")]
        [Comment("用户状态")]
        public byte Status { get; set; } = 1;

        /// <summary>
        /// 最后登录时间
        /// 记录用户最近一次登录的时间
        /// </summary>
        [Comment("最后登录时间")]
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 最后登录IP
        /// 记录用户最近一次登录的IP地址
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        [Comment("最后登录IP")]
        public string? LastLoginIp { get; set; }



        /// <summary>
        /// 用户角色关联集合
        /// 用于存储用户与角色的多对多关系
        /// </summary>
        public virtual ICollection<SysUserRole> UserRoles { get; set; } = [];

    }
}