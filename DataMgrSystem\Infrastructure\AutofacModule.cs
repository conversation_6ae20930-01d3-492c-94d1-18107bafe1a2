using Autofac;
using BLL.CardService;
using Common;
using Common.Autofac;
using Common.Export;
using DAL;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace DataMgrSystem.Infrastructure
{
    /// <summary>
    /// Autofac模块
    /// </summary>
    public class AutofacModule : Autofac.Module
    {
        /// <summary>
        /// 加载Autofac模块
        /// </summary>
        /// <param name="builder">容器构建器</param>
        protected override void Load(ContainerBuilder builder)
        {
            try
            {
                // 获取所有非动态程序集
                var assemblies = AppDomain.CurrentDomain.GetAssemblies()
                    .Where(a => !a.IsDynamic &&
                               a.FullName != null &&
                               !a.FullName.StartsWith("System.") &&
                               !a.FullName.StartsWith("Microsoft."))
                    .ToArray();

                // 注册DbContext
                builder.Register(c =>
                {
                    var configuration = c.Resolve<IConfiguration>();
                    var connectionString = configuration.GetSection("BasisConfig:DataBaseConnectionStrings:MysqlConnectionString").Value
                        ?? throw new InvalidOperationException("MySQL connection string not found.");

                    var serverVersion = ServerVersion.AutoDetect(connectionString);

                    var optionsBuilder = new DbContextOptionsBuilder<MyContext>();
                    optionsBuilder.UseMySql(connectionString, serverVersion);

                    return new MyContext(optionsBuilder.Options);
                })
                .AsSelf()
                .As<MyContext>()
                .InstancePerLifetimeScope();

                //// 注册仓储
                //builder.RegisterGeneric(typeof(Repository<>))
                //    .As(typeof(IRepository<>))
                //    .InstancePerLifetimeScope();

                // 注册导出服务
                builder.RegisterType<ExportService>()
                    .As<IExportService>()
                    .InstancePerLifetimeScope();

                // 注册导入服务
                builder.RegisterType<ImportService>()
                    .As<IImportService>()
                    .InstancePerLifetimeScope();

                // 注册基础服务
                builder.RegisterType<HttpContextAccessor>()
                    .As<IHttpContextAccessor>()
                    .SingleInstance();

                // 注册HttpClientFactory
                builder.Register(c => c.Resolve<IServiceProvider>().GetRequiredService<IHttpClientFactory>())
                    .As<IHttpClientFactory>()
                    .InstancePerLifetimeScope();

                // 显式注册ExternalGameServer和它的依赖项
                builder.RegisterType<ExternalGameServer>()
                    .AsSelf()
                    .InstancePerLifetimeScope();

                // 自动注册所有DAL和BLL类
                foreach (var assembly in assemblies)
                {
                    try
                    {
                        // 注册所有DAL类
                        var dalTypes = assembly.GetTypes()
                            .Where(t => t.IsClass && !t.IsAbstract &&
                                      (t.Name.EndsWith("DAL") || t.Namespace?.Contains(".DAL.") == true));

                        foreach (var dalType in dalTypes)
                        {
                            builder.RegisterType(dalType)
                                .AsSelf()
                                .InstancePerLifetimeScope();
                        }

                        // 注册所有BLL类和Service类
                        var bllTypes = assembly.GetTypes()
                            .Where(t => t.IsClass && !t.IsAbstract &&
                                      (t.Name.EndsWith("BLL") ||
                                       t.Name.EndsWith("Service") ||
                                       t.Namespace?.Contains(".BLL.") == true));

                        foreach (var bllType in bllTypes)
                        {
                            builder.RegisterType(bllType)
                                .AsSelf()
                                .InstancePerLifetimeScope();
                        }
                    }
                    catch (ReflectionTypeLoadException)
                    {
                        // 忽略无法加载的程序集
                        continue;
                    }
                }

                // 注册配置
                builder.Register(c =>
                {
                    var config = c.Resolve<IConfiguration>()
                        .GetSection("BasisConfig")
                        .Get<BasisConfigDto>();
                    return config ?? throw new InvalidOperationException("BasisConfig not found in configuration.");
                })
                .AsSelf()
                .SingleInstance();

                // 使用约定注册
                builder.RegisterByConvention(assemblies);

                // 使用特性注册
                builder.RegisterByAttribute(assemblies);

                base.Load(builder);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in Autofac module registration: {ex.Message}");
                throw;
            }
        }
    }
}