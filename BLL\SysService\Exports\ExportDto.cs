namespace BLL.SysService.Exports
{
    /// <summary>
    /// 导出列配置
    /// </summary>
    public class ExportColumnDto
    {
        /// <summary>
        /// 列标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 属性名
        /// </summary>
        public string PropertyName { get; set; } = string.Empty;

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 日期时间格式
        /// </summary>
        public string? Format { get; set; }
    }

    /// <summary>
    /// 导出请求DTO
    /// </summary>
    public class ExportRequestDto
    {
        /// <summary>
        /// 导出列配置
        /// </summary>
        public List<ExportColumnDto> Columns { get; set; } = [];

        /// <summary>
        /// 文件名
        /// </summary>
        public string? FileName { get; set; }
    }
}