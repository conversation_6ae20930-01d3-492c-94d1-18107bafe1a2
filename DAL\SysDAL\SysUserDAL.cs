using Common.Autofac;
using DAL.Databases;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;
using static DAL.SysDAL.SysUserDAL;

namespace DAL.SysDAL
{
    /// <summary>
    /// 用户数据访问层实现类
    /// 负责用户相关的数据库操作
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="context">数据库上下文</param>
    [Dependency(DependencyType.Scoped)]
    public class SysUserDAL(MyContext context) : BaseQueryDLL<SysUser, UserDALQuery>(context)
    {
        /// <summary>
        /// 数据库上下文实例
        /// </summary>
        private readonly MyContext _context = context;

        /// <summary>
        /// 用户查询条件模型类
        /// 定义了用户查询时可用的过滤条件
        /// </summary>
        public class UserDALQuery : PageQueryEntity
        {
            /// <summary>
            /// 用户ID
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? UserId { get; set; }

            /// <summary>
            /// 用户名
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? UserName { get; set; }

            /// <summary>
            /// 真实姓名
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? RealName { get; set; }

            /// <summary>
            /// 手机号
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Mobile { get; set; }

            /// <summary>
            /// 电子邮箱
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Email { get; set; }

            /// <summary>
            /// 用户状态
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? Status { get; set; }

            /// <summary>
            /// 创建时间范围开始
            /// </summary>
            [Query(QueryOperator.日期范围, columnName: "CreateTime", relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 创建时间范围结束
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 创建时间排序标志
            /// 用于按创建时间降序排序
            /// </summary>
            [Query(QueryOperator.排序, columnName: "CreateTime", orderDirection: OrderDirection.降序, orderPriority: 1)]
            public int? OrderByCreateTime { get; set; }
        }

        /// <summary>
        /// 检查用户名是否已存在
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <returns>存在返回true,不存在返回false</returns>
        public Task<bool> ExistsUserNameAsync(string userName, string? excludeUserId = null)
        {
            var query = _context.SysUsers.AsQueryable();
            if (!string.IsNullOrEmpty(excludeUserId))
                query = query.Where(u => u.UserId != excludeUserId);

            return query.AnyAsync(u => u.UserName == userName);
        }



        /// <summary>
        /// 检查用户信息唯一性（用户名、邮箱、手机号）
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <param name="email">邮箱</param>
        /// <param name="mobile">手机号</param>
        /// <param name="excludeUserId">排除的用户ID，用于更新时检查</param>
        /// <returns>
        /// 返回存在重复的字段名和值的字典。
        /// 如果字典为空，表示没有重复；否则，字典包含重复的字段名和值。
        /// </returns>
        public async Task<Dictionary<string, string>> CheckUserInfoUniqueAsync(
            string? userName,
            string? email,
            string? mobile,
            string? excludeUserId = null)
        {
            var result = new Dictionary<string, string>();
            var query = _context.SysUsers.AsQueryable();

            // 排除当前用户
            if (!string.IsNullOrEmpty(excludeUserId))
                query = query.Where(u => u.UserId != excludeUserId);

            // 执行一次查询获取所有可能重复的用户
            var existingUsers = await query
                .Where(u => (!string.IsNullOrEmpty(userName) && u.UserName == userName) ||
                            (!string.IsNullOrEmpty(email) && u.Email == email) ||
                            (!string.IsNullOrEmpty(mobile) && u.Mobile == mobile))
                .Select(u => new { u.UserName, u.Email, u.Mobile })
                .ToListAsync();

            // 检查用户名是否重复
            if (!string.IsNullOrEmpty(userName) && existingUsers.Any(u => u.UserName == userName))
                result.Add("UserName", userName);

            // 检查邮箱是否重复
            if (!string.IsNullOrEmpty(email) && existingUsers.Any(u => u.Email == email))
                result.Add("Email", email);

            // 检查手机号是否重复
            if (!string.IsNullOrEmpty(mobile) && existingUsers.Any(u => u.Mobile == mobile))
                result.Add("Mobile", mobile);

            return result;
        }

        /// <summary>
        /// 分页获取用户列表
        /// </summary>
        /// <param name="query">查询条件，包含分页参数和筛选条件</param>
        /// <returns>分页结果，包含用户列表和总记录数</returns>
        public Task<PageEntity<UserDto>> GetPageAsync(UserDALQuery queryable)

           // 直接使用已经实现好的高效率方法
           => GetUserPageWithRolesAsync(queryable);


        /// <summary>
        /// 添加用户角色关联
        /// </summary>
        /// <param name="userRoles">用户角色关联列表</param>
        public async Task<bool> AddUserRolesAsync(List<SysUserRole> userRoles)
        {
            await _context.SysUserRoles.AddRangeAsync(userRoles);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取用户的角色列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户的角色列表</returns>
        public async Task<List<SysRole>> GetUserRolesAsync(string userId)
        {
            var roleIds = await _context.SysUserRoles
                .Where(ur => ur.UserId == userId)
                .Select(ur => ur.RoleId)
                .ToListAsync();

            return await _context.SysRoles
                .Where(r => roleIds.Contains(r.Id))
                .ToListAsync();
        }

        /// <summary>
        /// 获取用户关联的角色名称列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>角色名称列表，如果用户没有关联角色则返回空列表</returns>
        public async Task<List<string>> GetUserRoleNamesAsync(string userId)
        {
            var roles = await GetUserRolesAsync(userId);
            return [.. roles.Select(r => r.Name)];
        }

        /// <summary>
        /// 获取用户关联的角色信息（包含角色名称和编码）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户角色信息DTO列表，如果用户没有关联角色则返回空列表</returns>
        public async Task<List<UserRoleDto>> GetUserRoleInfoAsync(string userId)
        {
            var roles = await GetUserRolesAsync(userId);
            return [.. roles.Select(r => new UserRoleDto
            {
                RoleId = r.Id,
                RoleName = r.Name,
                RoleCode = r.Code,
                Description = string.Empty // SysRole没有Description属性，设置为空字符串
            })];
        }

        /// <summary>
        /// 移除用户的所有角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        public async Task<bool> RemoveUserRolesAsync(string userId)
        {
            var userRoles = await _context.SysUserRoles
                .Where(ur => ur.UserId == userId)
                .ToListAsync();

            _context.SysUserRoles.RemoveRange(userRoles);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取分页数据列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="queryable">查询条件模型</param>
        /// <returns>分页结果</returns>
        public Task<PageEntity<SysUser>> GetPageDataAsync(
            UserDALQuery queryable)
        => GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.CreateTime));


        /// <summary>
        /// 获取用户分页列表（包含角色信息）
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>包含角色信息的用户分页数据</returns>
        public async Task<PageEntity<UserDto>> GetUserPageWithRolesAsync(UserDALQuery queryable)
        {
            // 先获取基本的分页数据
            var result = await GetPageDataAsync(queryable);

            if (result.List == null || result.List.Count == 0)
            {
                return new PageEntity<UserDto>
                {
                    List = [],
                    TotalCount = 0,
                    PageIndex = queryable.PageIndex,
                    PageSize = queryable.PageSize
                };
            }

            // 获取所有用户ID
            var userIds = result.List.Select(u => u.UserId).ToList();

            // 查询用户角色关联
            var userRoles = await _context.SysUserRoles
                .Where(ur => userIds.Contains(ur.UserId))
                .ToListAsync();

            // 获取所有涉及的角色ID
            var roleIds = userRoles.Select(ur => ur.RoleId).Distinct().ToList();

            // 查询角色信息
            var roles = await _context.SysRoles
                .Where(r => roleIds.Contains(r.Id))
                .ToListAsync();

            // 构建用户角色映射字典，一个用户可能有多个角色
            var userRoleMap = userRoles
                .GroupBy(ur => ur.UserId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(ur => ur.RoleId).ToList()
                );

            // 构建角色信息字典
            var roleMap = roles.ToDictionary(r => r.Id, r => r);

            // 转换为DTO并填充角色信息
            var userDtos = result.List.Select(user =>
            {
                var dto = new UserDto
                {
                    Id = user.UserId,
                    UserName = user.UserName,
                    RealName = user.RealName,
                    Avatar = user.Avatar,
                    Email = user.Email,
                    Mobile = user.Mobile,
                    Status = user.Status,
                    LastLoginTime = user.LastLoginTime,
                    LastLoginIp = user.LastLoginIp ?? string.Empty,
                    CreateTime = user.CreateTime
                };

                // 如果用户有角色，则填充第一个角色的信息（可以根据需求修改为多角色显示逻辑）
                if (userRoleMap.TryGetValue(user.UserId, out var userRoleIds) &&
                    userRoleIds.Count != 0 &&
                    roleMap.TryGetValue(userRoleIds.First(), out var role))
                {
                    dto.RoleName = role.Name;
                    dto.RoleCode = role.Code;
                }
                else
                {
                    dto.RoleName = string.Empty;
                    dto.RoleCode = string.Empty;
                }

                return dto;
            }).ToList();

            return new PageEntity<UserDto>
            {
                List = userDtos,
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }



    }
}