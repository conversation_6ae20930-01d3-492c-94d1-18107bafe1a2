﻿using Common.Log4Net;
using Microsoft.Extensions.Configuration;

namespace Common
{
    /// <summary>
    /// 阿里云短信发送服务
    /// </summary>
    public class AliSMSSender
    {
        private readonly string _endpoint;
        private readonly string _accessKeyId;
        private readonly string _accessKeySecret;
        private readonly string _signName;

        /// <summary>
        /// 通过依赖注入初始化
        /// </summary>
        public AliSMSSender(IConfiguration configuration)
        {
            _endpoint = configuration["AliSMS:Endpoint"] ?? "dysmsapi.aliyuncs.com";
            _accessKeyId = configuration["AliSMS:AccessKeyId"] ?? string.Empty;
            _accessKeySecret = configuration["AliSMS:AccessKeySecret"] ?? string.Empty;
            _signName = configuration["AliSMS:SignName"] ?? "唐人游";
        }

        /// <summary>
        /// 手动初始化
        /// </summary>
        public AliSMSSender(string endpoint, string accessKeyId, string accessKeySecret, string signName)
        {
            _endpoint = endpoint;
            _accessKeyId = accessKeyId;
            _accessKeySecret = accessKeySecret;
            _signName = signName;
        }

        /// <summary>
        /// 发送通知短信
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <param name="content">短信内容</param>
        /// <param name="sendTime">发送时间</param>
        /// <param name="errorMsg">错误信息输出参数</param>
        /// <returns>成功返回1，失败返回错误码</returns>
        public int SendNotification(string phoneNumber, string content, string sendTime, out string errorMsg)
        {
            return Send(phoneNumber, content, "SMS_461845145",
                new Dictionary<string, string>
                {
                    { "name", "唐人游用户" },
                    { "time", sendTime },
                    { "opt", content }
                }, out errorMsg);
        }

        /// <summary>
        /// 发送验证码短信
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <param name="code">验证码</param>
        /// <param name="errorMsg">错误信息输出参数</param>
        /// <returns>成功返回1，失败返回错误码</returns>
        public int SendVerificationCode(string phoneNumber, string code, out string errorMsg)
        {
            // 使用验证码模板，根据实际申请的模板修改模板ID
            return Send(phoneNumber, code, "SMS_461845145",
                new Dictionary<string, string>
                {
                    { "opt", $"卡充系统登录操作，验证码：{code}" },
                    { "name", "唐人游用户" },
                    { "time", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") }
                }, out errorMsg);
        }

        /// <summary>
        /// 异步发送验证码短信
        /// </summary>
        public async Task<(bool Success, string ErrorMessage)> SendVerificationCodeAsync(string phoneNumber, string code)
        {
            return await Task.Run(() =>
            {
                int result = SendVerificationCode(phoneNumber, code, out string errorMsg);
                return (result == 1, errorMsg);
            });
        }

        /// <summary>
        /// 发送短信
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <param name="content">内容</param>
        /// <param name="sendTime">发送时间</param>
        /// <param name="templateCode">模板代码</param>
        /// <param name="templateParams">模板参数</param>
        /// <param name="errorMsg">错误信息</param>
        /// <returns>成功返回1，失败返回错误码</returns>
        public int Send(string phoneNumber, string content, string templateCode,
            Dictionary<string, string> templateParams, out string errorMsg)
        {
            try
            {
                // 创建配置
                AlibabaCloud.OpenApiClient.Models.Config config = new()
                {
                    AccessKeyId = _accessKeyId,
                    AccessKeySecret = _accessKeySecret,
                    Endpoint = _endpoint
                };

                AlibabaCloud.OpenApiClient.Client client = new(config);

                AlibabaCloud.OpenApiClient.Models.Params params_ = new()
                {
                    Action = "SendSms",
                    Version = "2017-05-25",
                    Protocol = "HTTPS",
                    Method = "POST",
                    AuthType = "AK",
                    Style = "RPC",
                    Pathname = "/",
                    ReqBodyType = "json",
                    BodyType = "json",
                };

                // 构建请求参数
                Dictionary<string, object> queries = new()
                {
                    ["PhoneNumbers"] = phoneNumber,
                    ["SignName"] = _signName,
                    ["TemplateCode"] = templateCode
                };

                // 构建模板参数JSON
                string templateParamJson = AlibabaCloud.TeaUtil.Common.ToJSONString(templateParams);
                queries["TemplateParam"] = templateParamJson;

                // 发送请求
                AlibabaCloud.TeaUtil.Models.RuntimeOptions runtime = new();
                AlibabaCloud.OpenApiClient.Models.OpenApiRequest request = new()
                {
                    Query = AlibabaCloud.OpenApiUtil.Client.Query(queries),
                };

                Dictionary<string, object>? resp = null;
                try
                {
                    resp = client.CallApi(params_, request, runtime);
                }
                catch (Exception e)
                {
                    throw new Exception("发送短信失败: " + e.Message, e);

                }

                // 处理响应
                string message = "";
                string headers = "";
                int statusCode = 9999;

                if (resp != null && resp["body"] != null)
                {
                    if (!(resp["body"] is not Dictionary<string, object> body || body == null || !body.ContainsKey("Message")))
                    {
                        message = body["Message"]?.ToString() ?? "";
                    }
                }

                if (resp != null && resp["headers"] != null)
                {
                    if (resp["headers"] is Dictionary<string, string> _headers && _headers != null && _headers.Count > 0)
                    {
                        headers = AlibabaCloud.TeaUtil.Common.ToJSONString(_headers);
                    }
                }

                if (resp != null && resp["statusCode"] != null)
                {
                    if (resp["statusCode"] is int code)
                    {
                        statusCode = code;
                    }
                }

                if (statusCode != 200)
                {
                    throw new Exception($"发送短信失败: statusCode={statusCode}, message={message}, headers={headers}");
                }

                if (!message.Trim(' ', '\t', '\r', '\n').Equals("ok", StringComparison.CurrentCultureIgnoreCase))
                {
                    throw new Exception($"发送短信失败: statusCode={statusCode}, message={message}, headers={headers}");
                }

                LoggerHelper<AliSMSSender>.Info($"短信发送成功: 手机号={phoneNumber}, 内容={content}");
                errorMsg = message;
                return 1;
            }
            catch (Exception ex)
            {
                throw new Exception("发送短信失败: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 异步发送短信
        /// </summary>
        public async Task<(bool Success, string ErrorMessage)> SendAsync(string phoneNumber, string content, string templateCode, Dictionary<string, string> templateParams)
        {
            return await Task.Run(() =>
            {
                int result = Send(phoneNumber, content, templateCode, templateParams, out string errorMsg);
                return (result == 1, errorMsg);
            });
        }
    }
}
