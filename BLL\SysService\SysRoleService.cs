using Common.Autofac;
using Common.Exceptions;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Entity.Extensions;
using static DAL.Databases.EFHelper;

namespace BLL.SysService
{
    /// <summary>
    /// 角色服务实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class SysRoleService(SysRoleDAL roleDLL)
    {
        /// <summary>
        /// 角色数据访问层
        /// </summary>
        private readonly SysRoleDAL _roleDLL = roleDLL;

        /// <summary>
        /// 创建角色
        /// </summary>
        public async Task<string> CreateAsync(CreateRoleDto input, CurrentUserInfoDto currentUserInfo)
        {
            // 检查角色编码是否存在
            if (await _roleDLL.ExistsCodeAsync(input.Code))
                throw new BusinessException($"角色编码 {input.Code} 已存在");


            // 创建角色实体
            var role = new SysRole
            {
                Name = input.Name,
                Code = input.Code,
                OrderNum = input.OrderNum,
                DataScope = input.DataScope,
                Status = input.Status
            };

            // 初始化基础字段（ID、创建时间等）
            role.InitializeForAdd(currentUserInfo);

            // 添加角色
            await _roleDLL.AddAsync(role);

            return role.Id;
        }

        /// <summary>
        /// 更新角色
        /// </summary>
        public async Task UpdateAsync(UpdateRoleDto input, CurrentUserInfoDto currentUserInfo)
        {
            // 获取并验证角色是否存在
            var role = await _roleDLL.GetFirstAsync(new SysRoleDAL.Queryable { Id = input.Id })
                ?? throw new BusinessException($"角色 {input.Id} 不存在");

            // 检查角色编码是否存在（排除自身）
            if (await _roleDLL.ExistsCodeAsync(input.Code, input.Id))
                throw new BusinessException($"角色编码 {input.Code} 已存在");


            // 更新角色信息
            role.Name = input.Name;
            role.Code = input.Code;
            role.OrderNum = input.OrderNum;
            role.DataScope = input.DataScope;
            role.Status = input.Status;

            // 初始化更新字段（更新时间等）
            role.InitializeForUpdate(currentUserInfo);

            await _roleDLL.UpdateAsync(role);
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        public async Task DeleteAsync(string id)
        {
            // 获取并验证角色是否存在
            var role = await _roleDLL.GetFirstAsync(new SysRoleDAL.Queryable { Id = id })
                ?? throw new BusinessException($"角色 {id} 不存在");

            // 检查是否有用户关联此角色
            if (await _roleDLL.HasUsersAsync(id))
                throw new BusinessException("存在用户关联此角色，无法删除");


            await _roleDLL.DeleteAsync(role);
        }

        /// <summary>
        /// 获取角色详情
        /// </summary>
        public async Task<RoleDto> GetAsync(string id)
        {
            var role = await _roleDLL.GetFirstAsync(new SysRoleDAL.Queryable { Id = id })
                ?? throw new BusinessException($"角色 {id} 不存在");

            return new RoleDto
            {
                Id = role.Id,
                Name = role.Name,
                Code = role.Code,
                OrderNum = role.OrderNum,
                DataScope = role.DataScope,
                Status = role.Status,
                Remark = role.Remark,
                CreateTime = role.CreateTime,
                UpdateTime = role.UpdateTime
            };
        }

        /// <summary>
        /// 分页查询角色
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public Task<PageEntity<RoleDto>> GetPageAsync(SysRoleDAL.Queryable queryable)
        => _roleDLL.GetPageAsync(queryable);




        /// <summary>
        /// 获取角色下的用户列表
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>用户列表</returns>
        public async Task<List<UserDto>> GetRoleUsersAsync(string roleId)
        {
            // 验证角色是否存在
            if (!await _roleDLL.ExistsAsync(roleId))
                throw new BusinessException($"角色 {roleId} 不存在");


            return await _roleDLL.GetRoleUsersAsync(roleId);
        }

        /// <summary>
        /// 为角色分配用户
        /// </summary>
        public async Task AssignUsersAsync(AssignRoleUsersDto input, CurrentUserInfoDto currentUserInfo)
        {
            // 验证角色是否存在
            if (!await _roleDLL.ExistsAsync(input.RoleId))
                throw new BusinessException($"角色 {input.RoleId} 不存在");


            await _roleDLL.AssignUsersAsync(input.RoleId, input.UserIds, currentUserInfo);
            // 清空权限缓存
            SysPermissionService.ClearPermissionCache();
        }

        /// <summary>
        /// 获取用户的角色ID列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>角色ID列表</returns>
        public Task<List<string>> GetUserRolesAsync(string userId)
         => _roleDLL.GetUserRolesAsync(userId);

    }
}