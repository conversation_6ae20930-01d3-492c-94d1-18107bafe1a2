﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Linq.Expressions;
using System.Reflection;
using System.Security;

namespace DAL.Databases
{
    /// <summary>
    /// Entity Framework 扩展方法工具类
    /// </summary>
    public static class EFHelper
    {
        #region Where 条件扩展方法

        /// <summary>
        /// 条件Where扩展方法（函数条件）
        /// </summary>
        /// <typeparam name="TSource">实体类型</typeparam>
        /// <param name="source">查询源</param>
        /// <param name="ifFunc">条件判断函数</param>
        /// <param name="predicate">过滤条件表达式</param>
        /// <returns>根据条件过滤后的查询</returns>
        public static IQueryable<TSource> WhereIF<TSource>(
            this IQueryable<TSource> source,
            Func<bool> ifFunc,
            Expression<Func<TSource, bool>> predicate)
        {
            return ifFunc.Invoke() ? source.Where(predicate) : source;
        }

        /// <summary>
        /// 条件Where扩展方法（布尔条件）
        /// </summary>
        /// <typeparam name="TSource">实体类型</typeparam>
        /// <param name="source">查询源</param>
        /// <param name="ifBool">条件判断值</param>
        /// <param name="predicate">过滤条件表达式</param>
        /// <returns>根据条件过滤后的查询</returns>
        public static IQueryable<TSource> WhereIF<TSource>(
            this IQueryable<TSource> source,
            bool ifBool,
            Expression<Func<TSource, bool>> predicate)
        {
            return ifBool ? source.Where(predicate) : source;
        }

        #endregion

        #region DataTable 转换方法

        /// <summary>
        /// 将DataTable转换为指定类型的列表
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="dt">源DataTable</param>
        /// <returns>转换后的对象列表</returns>
        public static List<T> ToList<T>(this DataTable dt) where T : class, new()
        {
            var propertyInfos = typeof(T).GetProperties();
            var list = new List<T>();

            foreach (DataRow row in dt.Rows)
            {
                var t = new T();
                foreach (PropertyInfo p in propertyInfos)
                {
                    if (dt.Columns.IndexOf(p.Name) != -1 && row[p.Name] != DBNull.Value)
                    {
                        p.SetValue(t, row[p.Name], null);
                    }
                }
                list.Add(t);
            }

            return list;
        }

        #endregion

        #region 分页相关类和方法

        /// <summary>
        /// 分页基础信息类
        /// </summary>
        public class PageEntity
        {
            /// <summary>当前页码</summary>
            public int PageIndex { get; set; }

            /// <summary>每页大小</summary>
            public int PageSize { get; set; }

            /// <summary>总记录数</summary>
            public int TotalCount { get; set; }

            /// <summary>总页数</summary>
            public int TotalPages
            {
                get
                {
                    if (PageSize == 0) return 0;
                    var totalPages = TotalCount / PageSize;
                    if (TotalCount % PageSize > 0) totalPages++;
                    return totalPages;
                }
            }

            /// <summary>是否有上一页</summary>
            public bool HasPrevPages => PageIndex > 1;

            /// <summary>是否有下一页</summary>
            public bool HasNextPages => PageIndex < TotalPages;
        }

        /// <summary>
        /// 分页查询条件类
        /// </summary>
        public class PageQueryEntity
        {

            /// <summary>当前页码</summary>
            public int PageIndex { get; set; }

            /// <summary>每页大小</summary>
            public int PageSize { get; set; }

        }

        /// <summary>
        /// 分页数据类（泛型）
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        public class PageEntity<T> : PageEntity
        {
            /// <summary>当前页数据列表</summary>
            public List<T>? List { get; set; } = [];

            public PageEntity() { }

            public PageEntity(IQueryable<T> source, int pageIndex, int pageSize)
            {
                if (source != null && source.Any())
                {
                    TotalCount = source.Count();
                    PageSize = pageSize;
                    PageIndex = pageIndex;
                    List = [.. source.Skip((pageIndex - 1) * pageSize).Take(pageSize)];
                }
                else
                {
                    TotalCount = 0;
                    PageSize = pageSize;
                    PageIndex = pageIndex;
                    List = [];
                    return;
                }
            }

            public PageEntity(IList<T> source, int pageIndex, int pageSize)
            {
                ArgumentNullException.ThrowIfNull(source);

                TotalCount = source.Count;
                PageSize = pageSize;
                PageIndex = pageIndex;
                List = [.. source.Skip((pageIndex - 1) * pageSize).Take(pageSize)];
            }
        }

        /// <summary>
        /// 分页扩展方法
        /// </summary>
        public static PageEntity<T> ToPageList<T>(this IQueryable<T> query, PageEntity page)
        {
            var result = new PageEntity<T>
            {
                TotalCount = query.Count(),
                PageIndex = Math.Max(1, page.PageIndex),
                PageSize = Math.Max(10, page.PageSize)
            };

            result.List = [.. query
                .Skip((result.PageIndex - 1) * result.PageSize)
                .Take(result.PageSize)];

            return result;
        }

        /// <summary>
        /// 异步分页扩展方法
        /// </summary>
        public static async Task<PageEntity<T>> ToPageListAsync<T>(this IQueryable<T> query, PageEntity page)
        {
            var result = new PageEntity<T>
            {
                TotalCount = query.Count(),
                PageIndex = Math.Max(1, page.PageIndex),
                PageSize = Math.Max(10, page.PageSize)
            };

            result.List = await query
                .Skip((result.PageIndex - 1) * result.PageSize)
                .Take(result.PageSize)
                .ToListAsync();

            return result;
        }

        #endregion

        #region SQL 执行方法

        /// <summary>
        /// 执行SQL查询并返回第一个标量结果（值类型）
        /// </summary>
        /// <typeparam name="T">返回值类型（必须是值类型）</typeparam>
        /// <param name="context">数据库上下文实例</param>
        /// <param name="sql">SQL查询语句</param>
        /// <param name="parameters">查询参数字典</param>
        /// <returns>查询结果，可能为null</returns>
        /// <remarks>
        /// 示例：
        /// var count = await dbContext.ExecuteScalarAsync<int>(
        ///     "SELECT COUNT(*) FROM Users WHERE Age > @age",
        ///     new Dictionary<string, object> { { "@age", 18 } });
        /// </remarks>
        public static async Task<T?> ExecuteScalarAsync<T>(
            this DbContext context,
            string sql,
            Dictionary<string, object> parameters) where T : struct
        {
            // 参数校验
            ArgumentNullException.ThrowIfNull(context);
            ValidateSql(sql);

            // 创建参数列表
            var sqlParameters = parameters?
                .Select(p => new SqlParameter(p.Key, p.Value ?? DBNull.Value))
                .ToArray() ?? [];

            // 使用参数化查询执行SQL
            return await context.Database
                .SqlQueryRaw<T>(sql, sqlParameters)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 执行SQL查询并返回第一个标量结果（引用类型）
        /// </summary>
        /// <typeparam name="T">返回值类型（必须是引用类型）</typeparam>
        /// <param name="context">数据库上下文实例</param>
        /// <param name="sql">SQL查询语句</param>
        /// <param name="parameters">查询参数字典</param>
        /// <returns>查询结果，可能为null</returns>
        /// <remarks>
        /// 示例：
        /// var name = await dbContext.ExecuteScalarRefAsync<string>(
        ///     "SELECT Name FROM Users WHERE Id = @id",
        ///     new Dictionary<string, object> { { "@id", userId } });
        /// </remarks>
        public static async Task<T?> ExecuteScalarRefAsync<T>(
            this DbContext context,
            string sql,
            Dictionary<string, object> parameters) where T : class
        {
            // 参数校验
            ArgumentNullException.ThrowIfNull(context);
            ValidateSql(sql);

            // 创建参数列表
            var sqlParameters = parameters?
                .Select(p => new SqlParameter(p.Key, p.Value ?? DBNull.Value))
                .ToArray() ?? [];

            // 使用参数化查询执行SQL
            return await context.Database
                .SqlQueryRaw<T>(sql, sqlParameters)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 执行SQL查询并返回实体列表
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="context">数据库上下文</param>
        /// <param name="sql">SQL查询语句</param>
        /// <param name="parameters">查询参数</param>
        /// <returns>实体列表</returns>
        public static async Task<List<T>> ExecuteSqlListAsync<T>(
            this DbContext context,
            string sql,
            Dictionary<string, object> parameters) where T : class
        {
            var commandParameters = parameters?
                .Select(p => new SqlParameter(p.Key, p.Value ?? DBNull.Value))
                .ToArray() ?? [];

            return await context.Database
                .SqlQuery<T>($"{sql}")
                .ToListAsync();
        }

        /// <summary>
        /// SQL语句安全验证
        /// </summary>
        /// <param name="sql">要验证的SQL语句</param>
        /// <exception cref="ArgumentNullException">当SQL语句为空时抛出</exception>
        /// <exception cref="SecurityException">当检测到潜在SQL注入风险时抛出</exception>
        private static void ValidateSql(string sql)
        {
            if (string.IsNullOrWhiteSpace(sql))
                throw new ArgumentNullException(nameof(sql));

            // 定义禁止的关键字
            var forbiddenKeywords = new[] { ";", "--", "/*", "*/", "xp_", "sp_" };

            // 检查是否包含危险关键字
            if (forbiddenKeywords.Any(k => sql.Contains(k, StringComparison.OrdinalIgnoreCase)))
            {
                throw new SecurityException("检测到潜在的SQL注入风险");
            }
        }

        #endregion
    }
}
