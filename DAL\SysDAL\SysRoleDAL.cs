using Common.Autofac;
using DAL.Databases;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Entity.Extensions;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.SysDAL
{
    /// <summary>
    /// 角色数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class SysRoleDAL(MyContext context) : BaseQueryDLL<SysRole, SysRoleDAL.Queryable>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 角色查询条件模型
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 角色ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? Id { get; set; }

            /// <summary>
            /// 角色名称
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Name { get; set; }

            /// <summary>
            /// 角色编码
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? Code { get; set; }

            /// <summary>
            /// 状态
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? Status { get; set; }

            /// <summary>
            /// 排序
            /// </summary>
            [Query(QueryOperator.排序, columnName: "OrderNum", orderDirection: OrderDirection.升序)]
            public int? OrderByOrderNum { get; set; }
        }

        /// <summary>
        /// 获取分页数据列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="queryable">查询条件模型</param>
        /// <returns>分页结果</returns>
        public Task<PageEntity<SysRole>> GetPageDataAsync(
            Queryable queryable)
        => GetPageDataAsync(queryable, q => q.OrderBy(x => x.OrderNum));


        /// <summary>
        /// 分页获取用户列表
        /// </summary>
        /// <param name="query">查询条件，包含分页参数和筛选条件</param>
        /// <returns>分页结果，包含用户列表和总记录数</returns>
        public async Task<PageEntity<RoleDto>> GetPageAsync(Queryable queryable)
        {
            var result = await GetPageDataAsync(queryable);

            // 转换为DTO
            return new PageEntity<RoleDto>
            {
                List = [.. (result.List ?? Enumerable.Empty<SysRole>()).Select(x => new RoleDto
                {
                    Id = x.Id,
                    Name = x.Name,
                    Code = x.Code,
                    Status = x.Status,
                    OrderNum = x.OrderNum,
                    CreateTime = x.CreateTime,
                    UpdateTime = x.UpdateTime,
                })],
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }




        /// <summary>
        /// 检查角色是否存在
        /// </summary>
        public Task<bool> ExistsAsync(string id)
        => _context.SysRoles.AnyAsync(r => r.Id == id);


        /// <summary>
        /// 检查角色编码是否存在
        /// </summary>
        public async Task<bool> ExistsCodeAsync(string code, string? excludeId = null)
        {
            var query = _context.SysRoles.AsQueryable();
            if (!string.IsNullOrEmpty(excludeId))
                query = query.Where(r => r.Id != excludeId);

            return await query.AnyAsync(r => r.Code == code);
        }

        /// <summary>
        /// 检查是否有用户关联此角色
        /// </summary>
        public Task<bool> HasUsersAsync(string roleId)
        => _context.SysUserRoles.AnyAsync(ur => ur.RoleId == roleId);




        /// <summary>
        /// 获取角色下的用户列表
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>用户列表</returns>
        public async Task<List<UserDto>> GetRoleUsersAsync(string roleId)
        {
            var users = await _context.SysUserRoles
                .Where(ur => ur.RoleId == roleId)
                .Include(ur => ur.User)
                .Where(ur => ur.User != null)  // 确保 User 不为空
                .Select(ur => new UserDto
                {
                    Id = ur.User!.UserId,      // 使用空断言，因为我们已经过滤了空值
                    UserName = ur.User.UserName,
                    RealName = ur.User.RealName,
                    Avatar = ur.User.Avatar,
                    Email = ur.User.Email,
                    Mobile = ur.User.Mobile,
                    Status = ur.User.Status,
                    LastLoginTime = ur.User.LastLoginTime,
                    LastLoginIp = ur.User.LastLoginIp ?? string.Empty,
                    CreateTime = ur.User.CreateTime,
                    UpdateTime = ur.User.CreateTime
                })
                .ToListAsync();

            return users;
        }

        /// <summary>
        /// 为角色分配用户
        /// </summary>
        public async Task<bool> AssignUsersAsync(string roleId, List<string> userIds, CurrentUserInfoDto currentUserInfo)
        {
            // 删除原有的角色用户关联
            var existingUserRoles = await _context.SysUserRoles
                .Where(ur => ur.RoleId == roleId)
                .ToListAsync();
            _context.SysUserRoles.RemoveRange(existingUserRoles);

            // 添加新的角色用户关联
            if (userIds.Count != 0)
            {
                var userRoles = userIds.Select(userId => new SysUserRole
                {
                    RoleId = roleId,
                    UserId = userId
                }).ToList();

                // 初始化基础字段
                foreach (var userRole in userRoles)
                {
                    userRole.InitializeForAdd(currentUserInfo);
                }

                await _context.SysUserRoles.AddRangeAsync(userRoles);
            }

            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取用户的角色ID列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>角色ID列表</returns>
        public async Task<List<string>> GetUserRolesAsync(string userId)
        {
            // 获取用户角色关联
            var userRoles = await _context.SysUserRoles
                .Where(ur => ur.UserId == userId)
                .ToListAsync();

            // 提取角色ID
            return [.. userRoles.Select(ur => ur.RoleId)];
        }


    }
}