using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.OrganizationDto
{
    /// <summary>
    /// 部门DTO
    /// </summary>
    public class DepartmentDto
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// 部门名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
        /// <summary>
        /// 部门编码
        /// </summary>
        public string Code { get; set; } = string.Empty;
        /// <summary>
        /// 父部门ID
        /// </summary>
        public string? ParentId { get; set; }
        /// <summary>
        /// 父部门名称
        /// </summary>
        public string ParentName { get; set; } = string.Empty;
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 路径
        /// </summary>
        public string Path { get; set; } = string.Empty;
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 部门树形结构DTO
    /// </summary>
    public class DepartmentTreeDto : DepartmentDto
    {
        public List<DepartmentTreeDto> Children { get; set; } = [];
    }

    /// <summary>
    /// 创建部门DTO
    /// </summary>
    public class CreateDepartmentDto
    {
        [Required]
        [MaxLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;

        public string? ParentId { get; set; }

        public int Sort { get; set; }

        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新部门DTO
    /// </summary>
    public class UpdateDepartmentDto
    {
        public string Id { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;

        public string? ParentId { get; set; }

        public int Sort { get; set; }

        public int Status { get; set; }

        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
    }
}