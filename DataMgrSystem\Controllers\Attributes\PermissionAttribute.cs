namespace DataMgrSystem.Controllers.Attributes
{

    /// <summary>
    /// 功能权限特性
    /// </summary>
    /// <remarks>
    /// 权限特性
    /// </remarks>
    /// <param name="code"> 权限编码 </param>
    /// <param name="description"> 权限描述 </param>
    [AttributeUsage(AttributeTargets.Method)]
    public class FunctionPermissionAttribute(string code, string description) : Attribute
    {
        /// <summary>
        /// 权限编码
        /// </summary>
        public string Code { get; } = code;

        /// <summary>
        /// 权限描述
        /// </summary>
        public string Description { get; } = description;
    }


    /// <summary>
    /// 权限特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class PermissionAttribute : Attribute
    {
    }
}