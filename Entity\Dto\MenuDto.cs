namespace Entity.Dto
{
    /// <summary>
    /// 菜单DTO
    /// </summary>
    public class MenuDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 父级ID
        /// </summary>
        public string? ParentId { get; set; }

        /// <summary>
        /// 菜单名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 路由路径
        /// </summary>
        public string? Path { get; set; }

        /// <summary>
        /// 组件路径
        /// </summary>
        public string? Component { get; set; }

        /// <summary>
        /// 权限标识
        /// </summary>
        public string? Perms { get; set; }

        /// <summary>
        /// 菜单图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 菜单类型（1：目录 2：菜单 3：按钮）
        /// </summary>
        public byte Type { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int OrderNum { get; set; }

        /// <summary>
        /// 是否显示（0：不显示 1：显示）
        /// </summary>
        public byte Visible { get; set; }

        /// <summary>
        /// 状态（0：禁用 1：正常）
        /// </summary>
        public byte Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 子菜单列表
        /// </summary>
        public List<MenuDto> Children { get; set; } = [];


        /// <summary>
        ///  
        /// </summary>
        /// <value></value>
        public List<ButtonDto> Buttons { get; set; } = [];
    }

    /// <summary>
    /// 创建菜单DTO
    /// </summary>
    public class CreateMenuDto
    {
        /// <summary>
        /// 父级ID
        /// </summary>
        public string? ParentId { get; set; }

        /// <summary>
        /// 菜单名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 路由路径
        /// </summary>
        public string? Path { get; set; }

        /// <summary>
        /// 组件路径
        /// </summary>
        public string? Component { get; set; }

        /// <summary>
        /// 权限标识
        /// </summary>
        public string? Perms { get; set; }

        /// <summary>
        /// 菜单图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 菜单类型（1：目录 2：菜单 3：按钮）
        /// </summary>
        public byte Type { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int OrderNum { get; set; }

        /// <summary>
        /// 是否显示（0：不显示 1：显示）
        /// </summary>
        public byte Visible { get; set; } = 1;

        /// <summary>
        /// 状态（0：禁用 1：正常）
        /// </summary>
        public byte Status { get; set; } = 1;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 更新菜单DTO
    /// </summary>
    public class UpdateMenuDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 父级ID
        /// </summary>
        public string? ParentId { get; set; }

        /// <summary>
        /// 菜单名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 路由路径
        /// </summary>
        public string? Path { get; set; }

        /// <summary>
        /// 组件路径
        /// </summary>
        public string? Component { get; set; }

        /// <summary>
        /// 权限标识
        /// </summary>
        public string? Perms { get; set; }

        /// <summary>
        /// 菜单图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 菜单类型（1：目录 2：菜单 3：按钮）
        /// </summary>
        public byte Type { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int OrderNum { get; set; }

        /// <summary>
        /// 是否显示（0：不显示 1：显示）
        /// </summary>
        public byte Visible { get; set; }

        /// <summary>
        /// 状态（0：禁用 1：正常）
        /// </summary>
        public byte Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    // /// <summary>
    // /// 查询菜单DTO
    // /// </summary>
    // public class QueryMenuDto
    // {
    //     /// <summary>
    //     /// 菜单名称
    //     /// </summary>
    //     public string? Name { get; set; }

    //     /// <summary>
    //     /// 状态（0：禁用 1：正常）
    //     /// </summary>
    //     public byte? Status { get; set; }
    // }
}