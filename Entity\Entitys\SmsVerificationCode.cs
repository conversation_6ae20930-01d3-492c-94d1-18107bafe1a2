using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys
{
    /// <summary>
    /// 短信验证码实体
    /// </summary>
    [Table("sms_verification_code")]
    public class SmsVerificationCode
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// 验证码
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 业务类型（如：login, register, resetpwd等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string BusinessType { get; set; } = string.Empty;

        /// <summary>
        /// 发送时间
        /// </summary>
        public DateTime SendTime { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpireTime { get; set; }

        /// <summary>
        /// 是否已使用
        /// </summary>
        public bool IsUsed { get; set; }

        /// <summary>
        /// 使用时间
        /// </summary>
        public DateTime? UsedTime { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        [StringLength(50)]
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// 设备标识
        /// </summary>
        [StringLength(500)]
        public string DeviceId { get; set; } = string.Empty;
    }
}