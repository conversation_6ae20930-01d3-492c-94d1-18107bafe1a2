namespace Entity;

/// <summary>
/// 批次状态
/// </summary>
public enum BatchStatusEnum
{
    /// <summary>
    /// 未激活
    /// </summary>
    未激活 = 0,
    /// <summary>
    /// 已激活
    /// </summary>
    已激活 = 1,
    /// <summary>
    /// 已出货
    /// </summary>
    已出货 = 2
}

/// <summary>
/// 卡片状态
/// </summary>
public enum CardStatusEnum
{
    /// <summary>
    /// 未核销
    /// </summary>
    未核销 = 0,
    /// <summary>
    /// 已核销
    /// </summary>
    已核销 = 1,
    /// <summary>
    /// 锁定中
    /// </summary>
    锁定中 = 2
}

/// <summary>
/// 核销状态
/// </summary>
public enum CardUseRecordStatusEnum
{
    /// <summary>
    /// 使用卡片
    /// </summary>
    使用卡片 = 1,
    /// <summary>
    /// 转移卡片
    /// </summary>
    转移卡片 = 0
}

/// <summary>
/// 卡片流转状态
/// </summary>
public enum CardTransferRecordStatusEnum
{
    /// <summary>
    /// 转卡
    /// </summary>
    转卡 = 0,
    /// <summary>
    /// 核销
    /// </summary>
    核销 = 1
}


