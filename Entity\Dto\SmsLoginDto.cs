using System.ComponentModel.DataAnnotations;

namespace Entity.Dto
{
    /// <summary>
    /// 短信验证码登录DTO
    /// </summary>
    public class SmsLoginDto
    {
        /// <summary>
        /// 手机号码
        /// </summary>
        [Required(ErrorMessage = "手机号码不能为空")]
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "手机号码格式不正确")]
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// 验证码
        /// </summary>
        [Required(ErrorMessage = "验证码不能为空")]
        [StringLength(6, MinimumLength = 4, ErrorMessage = "验证码长度必须在4-6位之间")]
        public string VerificationCode { get; set; } = string.Empty;
    }
}