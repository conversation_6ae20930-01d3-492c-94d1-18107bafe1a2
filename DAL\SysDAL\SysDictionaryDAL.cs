using Common.Autofac;
using DAL.Databases;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.SysDAL
{
    /// <summary>
    /// 系统字典表数据访问层实现类
    /// 负责字典相关的数据库操作
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="context">数据库上下文</param>
    [Dependency(DependencyType.Scoped)]
    public class SysDictionaryDAL(MyContext context) : BaseQueryDLL<SysDictionary, SysDictionaryDAL.Queryable>(context)
    {
        /// <summary>
        /// 数据库上下文实例
        /// </summary>
        private readonly MyContext _context = context;

        /// <summary>
        /// 字典查询条件模型类
        /// 定义了字典查询时可用的过滤条件
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 字典ID
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? Id { get; set; }


            /// <summary>
            /// 父级字典ID
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? ParentId { get; set; }

            /// <summary>
            /// 字典类型码
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? DictTypeCode { get; set; }

            /// <summary>
            /// 字典类型名称
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? DictTypeName { get; set; }

            /// <summary>
            /// 字典项编码
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? DictItemCode { get; set; }

            /// <summary>
            /// 字典项名称
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? DictItemName { get; set; }

            /// <summary>
            /// 是否启用
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public bool? IsEnabled { get; set; }

            /// <summary>
            /// 创建时间范围开始
            /// </summary>
            [Query(QueryOperator.日期范围, columnName: "CreateTime", relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 创建时间范围结束
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 排序号排序标志
            /// 用于按排序号升序排序
            /// </summary>
            [Query(QueryOperator.排序, columnName: "SortOrder", orderDirection: OrderDirection.升序, orderPriority: 1)]
            public int? OrderBySortOrder { get; set; }
        }

        /// <summary>
        /// 获取所有字典列表
        /// </summary>
        /// <returns>字典列表</returns>
        public async Task<List<SysDictionary>> GetAllDictionariesAsync()
        {
            var query = new Queryable { IsEnabled = true, OrderBySortOrder = 1 };
            return await GetListAsync(query, q => q.OrderBy(x => x.DictTypeCode).ThenBy(x => x.SortOrder));
        }

        /// <summary>
        /// 根据字典类型码获取字典项
        /// </summary>
        /// <param name="dictTypeCode">字典类型码</param>
        /// <returns>字典项列表</returns>
        public async Task<List<SysDictionary>> GetDictionariesByTypeCodeAsync(string dictTypeCode)
        {
            var query = new Queryable { DictTypeCode = dictTypeCode, IsEnabled = true, OrderBySortOrder = 1 };
            return await GetListAsync(query);
        }

        /// <summary>
        /// 添加字典项
        /// </summary>
        /// <param name="dictionary">字典项</param>
        /// <returns>添加结果</returns>
        public Task<bool> AddDictionaryAsync(SysDictionary dictionary)
       => AddAsync(dictionary);

        /// <summary>
        /// 批量添加字典项
        /// </summary>
        /// <param name="dictionaries">字典项列表</param>
        /// <returns>添加结果</returns>
        public Task<bool> AddDictionariesAsync(List<SysDictionary> dictionaries)
        => AddRangeAsync(dictionaries);


        /// <summary>
        /// 更新字典项
        /// </summary>
        /// <param name="dictionary">字典项</param>
        /// <returns>更新结果</returns>
        public Task<bool> UpdateDictionaryAsync(SysDictionary dictionary)
        => UpdateAsync(dictionary);


        /// <summary>
        /// 删除字典项
        /// </summary>
        /// <param name="id">字典项ID</param>
        /// <returns>删除结果</returns>
        public Task<bool> DeleteDictionaryAsync(string id)
        => DeleteByIdAsync(id);


        /// <summary>
        /// 根据字典类型码删除字典项
        /// </summary>
        /// <param name="dictTypeCode">字典类型码</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteDictionariesByTypeCodeAsync(string dictTypeCode)
        {
            var query = new Queryable { DictTypeCode = dictTypeCode };
            return await DeleteByConditionAsync(query);
        }

        /// <summary>
        /// 获取所有字典类型
        /// </summary>
        /// <returns>字典类型列表</returns>
        public Task<List<SysDictionary>> GetAllDictionaryTypesAsync()
        => _context.SysDictionaries
                .GroupBy(x => new { x.DictTypeCode, x.DictTypeName })
                .Select(g => new SysDictionary
                {
                    DictTypeCode = g.Key.DictTypeCode,
                    DictTypeName = g.Key.DictTypeName
                })
                .OrderBy(x => x.DictTypeCode)
                .ToListAsync();


        /// <summary>
        /// 获取分页字典数据
        /// </summary>
        /// <param name="queryable">分页查询条件</param>
        /// <returns>分页结果</returns>
        public Task<PageEntity<SysDictionary>> GetPageAsync(Queryable queryable)
        => GetPageDataAsync(queryable, q => q.OrderBy(x => x.DictTypeCode).ThenBy(x => x.SortOrder));


        /// <summary>
        /// 根据字典类型码获取下拉框数据
        /// </summary>
        /// <param name="dictTypeCode">字典类型码</param>
        /// <returns>下拉框数据</returns>
        public async Task<List<DictionaryDropdownDto>> GetDropdownListByTypeCodeAsync(string dictTypeCode)
        {
            var dictionaries = await GetDictionariesByTypeCodeAsync(dictTypeCode);
            return [.. dictionaries.Select(d => new DictionaryDropdownDto
            {
                Value = d.DictItemCode,
                Label = d.DictItemName,
                Disabled = !d.IsEnabled,
                SortOrder = d.SortOrder
            }).OrderBy(d => d.SortOrder)];
        }

        /// <summary>
        /// 获取所有父级字典（ParentId为空的字典）
        /// </summary>
        /// <returns>父级字典列表</returns>
        public async Task<List<SysDictionary>> GetAllParentDictionariesAsync()
        {
            var query = new Queryable { IsEnabled = true, OrderBySortOrder = 1 };
            return await _context.SysDictionaries
                .Where(x => x.ParentId == "" && x.IsEnabled == true)
                .OrderBy(x => x.DictTypeCode)
                .ThenBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 添加父级字典
        /// </summary>
        /// <param name="dictionary">父级字典实体</param>
        /// <returns>是否添加成功</returns>
        public async Task<bool> AddParentDictionaryAsync(SysDictionary dictionary)
        {
            dictionary.ParentId = ""; // 确保是父级字典
            return await AddDictionaryAsync(dictionary);
        }

        /// <summary>
        /// 删除父级字典及其所有子字典
        /// </summary>
        /// <param name="parentId">父级字典ID</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteParentDictionaryWithChildrenAsync(string parentId)
        {
            // 查找所有子字典
            var childDictionaries = await _context.SysDictionaries
                .Where(x => x.ParentId == parentId)
                .ToListAsync();

            // 删除所有子字典
            if (childDictionaries.Count != 0)
                _context.SysDictionaries.RemoveRange(childDictionaries);


            // 删除父级字典
            var parentDictionary = await _context.SysDictionaries.FindAsync(parentId);
            if (parentDictionary != null)
                _context.SysDictionaries.Remove(parentDictionary);


            return await _context.SaveChangesAsync() > 0;
        }
    }
}