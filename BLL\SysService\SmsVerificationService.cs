using Common;
using DAL.SysDAL;
using Entity.Entitys;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System.Text;

namespace BLL.SysService
{
    /// <summary>
    /// 短信验证码服务
    /// </summary>
    public class SmsVerificationService
    {
        private readonly AliSMSSender _smsSender;
        private readonly SmsVerificationCodeDAL _smsCodeDAL;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContextAccessor;

        // 验证码有效期（默认5分钟）
        private readonly TimeSpan _codeExpiration;

        /// <summary>
        /// 初始化短信验证码服务
        /// </summary>
        public SmsVerificationService(
            AliSMSSender smsSender,
            SmsVerificationCodeDAL smsCodeDAL,
            IConfiguration configuration,
            IHttpContextAccessor httpContextAccessor)
        {
            _smsSender = smsSender;
            _smsCodeDAL = smsCodeDAL;
            _configuration = configuration;
            _httpContextAccessor = httpContextAccessor;

            // 从配置中获取有效期（分钟），默认5分钟
            int expirationMinutes = configuration.GetValue<int>("SmsVerification:ExpirationMinutes", 5);
            _codeExpiration = TimeSpan.FromMinutes(expirationMinutes);
        }

        /// <summary>
        /// 生成随机验证码
        /// </summary>
        /// <param name="length">验证码长度，默认6位</param>
        /// <returns>生成的验证码</returns>
        public string GenerateCode(int length = 6)
        {
            // 根据配置获取验证码长度，如果未配置使用参数默认值
            int codeLength = _configuration.GetValue<int>("SmsVerification:CodeLength", length);

            StringBuilder code = new();
            Random random = new();

            for (int i = 0; i < codeLength; i++)
            {
                code.Append(random.Next(0, 10)); // 生成0-9之间的数字
            }

            return code.ToString();
        }

        /// <summary>
        /// 生成并发送验证码
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <param name="businessType">业务类型，如"login"、"register"等，用于区分不同业务场景</param>
        /// <returns>发送结果，包含是否成功和错误信息</returns>
        public async Task<(bool Success, string ErrorMessage)> SendVerificationCodeAsync(string phoneNumber, string businessType)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(phoneNumber))
                    return (false, "手机号码不能为空");

                // 生成验证码
                string code = GenerateCode();

                // 清除之前未使用的验证码
                await _smsCodeDAL.ClearUnusedCodesAsync(phoneNumber, businessType);

                // 获取客户端信息
                string ipAddress = GetClientIpAddress();
                string deviceId = GetClientDeviceId();

                // 创建验证码记录
                var verificationCode = new SmsVerificationCode
                {
                    PhoneNumber = phoneNumber,
                    Code = code,
                    BusinessType = businessType,
                    SendTime = DateTime.Now,
                    ExpireTime = DateTime.Now.Add(_codeExpiration),
                    IsUsed = false,
                    IpAddress = ipAddress,
                    DeviceId = deviceId
                };

                // 保存到数据库
                bool saveResult = await _smsCodeDAL.AddVerificationCodeAsync(verificationCode);
                if (!saveResult)
                {
                    throw new Exception("验证码保存失败");
                }

                // 发送验证码短信
                var result = await _smsSender.SendVerificationCodeAsync(phoneNumber, code);

                if (!result.Success)
                {
                    throw new Exception(
                        $"验证码发送失败：手机号 {phoneNumber}, 业务类型 {businessType}, 错误 {result.ErrorMessage}");
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception($"发送验证码异常：手机号 {phoneNumber}, 业务类型 {businessType}", ex);
            }
        }

        /// <summary>
        /// 校验验证码
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <param name="code">用户输入的验证码</param>
        /// <param name="businessType">业务类型，如"login"、"register"等</param>
        /// <param name="removeAfterValidation">验证后是否标记为已使用，默认为true</param>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidateCodeAsync(string phoneNumber, string code, string businessType, bool removeAfterValidation = true)
        {
            if (string.IsNullOrEmpty(phoneNumber) || string.IsNullOrEmpty(code))
                return false;


            return await _smsCodeDAL.ValidateCodeAsync(phoneNumber, code, businessType, removeAfterValidation);
        }

        /// <summary>
        /// 清除指定手机号和业务类型的验证码
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <param name="businessType">业务类型</param>
        /// <returns>是否清除成功</returns>
        public async Task<bool> ClearCodesAsync(string phoneNumber, string businessType)
        {
            if (string.IsNullOrEmpty(phoneNumber) || string.IsNullOrEmpty(businessType))
                return false;
            return await _smsCodeDAL.ClearUnusedCodesAsync(phoneNumber, businessType);
        }

        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        /// <returns>客户端IP地址</returns>
        private string GetClientIpAddress()
        {
            if (_httpContextAccessor?.HttpContext == null)
                return string.Empty;


            var remoteIp = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress;
            return remoteIp?.ToString() ?? string.Empty;
        }

        /// <summary>
        /// 获取客户端设备标识
        /// </summary>
        /// <returns>客户端设备标识</returns>
        private string GetClientDeviceId()
        {
            if (_httpContextAccessor?.HttpContext == null)
                return string.Empty;

            // 尝试从User-Agent获取设备信息
            if (_httpContextAccessor.HttpContext.Request.Headers.TryGetValue("User-Agent", out var userAgent))
                return userAgent.ToString();

            return string.Empty;
        }
    }
}