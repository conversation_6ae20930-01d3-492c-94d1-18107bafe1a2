using Azure.Core;
using Common;
using Common.Autofac;
using Common.Exceptions;
using Common.Helper;
using Common.JWT;
using Common.Redis;
using DAL.Databases;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Entity.Extensions;
using System.Security.Claims;
using static DAL.Databases.EFHelper;
using static DAL.SysDAL.SysUserDAL;

namespace BLL.SysService
{
    /// <summary>
    /// 用户服务实现类,处理用户相关的业务逻辑
    /// </summary>
    /// <remarks>
    /// 构造函数,通过依赖注入获取用户数据访问层实例
    /// </remarks>
    /// <param name="userDLL">用户数据访问层接口</param>
    /// <param name="permissionService">权限服务</param>
    /// <param name="departmentDLL">部门数据访问层接口</param>
    /// <param name="userRoleDLL">用户角色数据访问层接口</param>
    /// <param name="roleDLL">角色数据访问层接口</param>
    /// <param name="logger">日志记录器</param>
    [Dependency(DependencyType.Scoped)]
    public class SysUserService(SysUserDAL userDLL, SysPermissionService permissionService)
    {
        /// <summary>
        /// 用户数据访问层接口
        /// </summary>
        private readonly SysUserDAL _userDLL = userDLL;

        /// <summary>
        /// 权限服务
        /// </summary>
        private readonly SysPermissionService _permissionService = permissionService;

        /// <summary>
        /// Redis中存储用户登录状态的key前缀
        /// </summary>
        private const string USER_LOGIN_STATUS_PREFIX = "UserLoginStatus:";

        /// <summary>
        /// 用户登录状态过期时间（与JWT token过期时间保持一致）
        /// </summary>
        private static readonly TimeSpan LOGIN_STATUS_EXPIRY = TimeSpan.FromSeconds(JWTSetting.Exp);

        /// <summary>
        /// 获取用户的Redis key
        /// </summary>
        private static string GetUserKey(string userId) => $"{USER_LOGIN_STATUS_PREFIX}{userId}";



        /// <summary>
        /// 清除用户登录状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        public static void ClearUserLoginStatus(string userId) => RedisHelper.Remove(GetUserKey(userId));

        /// <summary>
        /// 创建新用户
        /// </summary>
        /// <param name="input">用户创建信息DTO</param>
        /// <returns>新创建用户的ID</returns>
        /// <exception cref="BusinessException">当用户名已存在时抛出异常</exception>
        public async Task<string> CreateAsync(CreateUserDto input, CurrentUserInfoDto currentUserInfo)
        {
            // 一次性验证用户名、邮箱和手机号的唯一性
            var duplicates = await _userDLL.CheckUserInfoUniqueAsync(
                input.UserName, input.Email, input.Mobile);

            // 如果有重复字段，抛出异常
            if (duplicates.Count > 0)
            {
                var firstDuplicate = duplicates.First();
                string fieldName = firstDuplicate.Key switch
                {
                    "UserName" => "用户名",
                    "Email" => "邮箱",
                    "Mobile" => "手机号",
                    _ => firstDuplicate.Key
                };
                throw new BusinessException($"{fieldName} {firstDuplicate.Value} 已被使用");
            }

            // 创建新用户实体并设置属性
            var user = new SysUser
            {
                UserId = Guid.NewGuid().ToString("N"),
                UserName = input.UserName,
                Password = EncryptHelper.EncryptPassword(input.Password), // 加密密码
                RealName = input.RealName,
                Avatar = input.Avatar,
                Email = input.Email,
                Mobile = input.Mobile,
                Status = 1,
            };
            user.InitializeForAdd(currentUserInfo);
            await _userDLL.AddAsync(user);
            return user.UserId;
        }



        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="input">用户更新信息DTO</param>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task UpdateAsync(UpdateUserDto input, CurrentUserInfoDto currentUserInfo)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = input.UserId })
                ?? throw new BusinessException($"用户 {input.UserId} 不存在");

            // 一次性验证邮箱和手机号的唯一性（更新时不需要验证用户名）
            var duplicates = await _userDLL.CheckUserInfoUniqueAsync(
                null, input.Email, input.Mobile, input.UserId);

            // 如果有重复字段，抛出异常
            if (duplicates.Count > 0)
            {
                var firstDuplicate = duplicates.First();
                string fieldName = firstDuplicate.Key switch
                {
                    "Email" => "邮箱",
                    "Mobile" => "手机号",
                    _ => firstDuplicate.Key
                };
                throw new BusinessException($"{fieldName} {firstDuplicate.Value} 已被其他用户使用");
            }

            // 更新用户信息
            user.RealName = input.RealName;
            user.Avatar = input.Avatar;
            user.Email = input.Email;
            user.Mobile = input.Mobile;
            user.Status = input.Status;
            user.InitializeForUpdate(currentUserInfo);
            await _userDLL.UpdateAsync(user);
        }

        /// <summary>
        /// 删除指定用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task DeleteAsync(string userId)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = userId })
                ?? throw new BusinessException($"用户 {userId} 不存在");

            await _userDLL.DeleteAsync(user);
        }

        /// <summary>
        /// 获取用户详细信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户详细信息DTO</returns>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task<UserDto> GetAsync(string userId)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = userId })
                ?? throw new BusinessException($"用户 {userId} 不存在");

            // 转换为DTO并返回
            return new UserDto
            {
                Id = user.UserId,
                UserName = user.UserName,
                RealName = user.RealName,
                Avatar = user.Avatar,
                Email = user.Email,
                Mobile = user.Mobile,
                Status = user.Status,
                LastLoginTime = user.LastLoginTime,
                LastLoginIp = user.LastLoginIp ?? string.Empty,
                CreateTime = user.CreateTime,
            };
        }

        /// <summary>
        /// 分页查询用户列表
        /// </summary>
        /// <param name="queryable">查询条件DTO</param>
        /// <returns>分页后的用户列表</returns>
        public async Task<PageEntity<UserDto>> GetPageAsync(UserDALQuery queryable)
        {
            var page = await _userDLL.GetPageAsync(queryable);
            if (page.List != null)
                page.List = [.. page.List.Where(t => t.UserName != "admin")];

            return page;
        }

        /// <summary>
        /// 修改用户密码
        /// </summary>
        /// <param name="input">密码修改信息DTO</param>
        /// <exception cref="BusinessException">当用户不存在或旧密码不正确时抛出异常</exception>
        public async Task ChangePasswordAsync(ChangePasswordDto input)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = input.UserId })
                ?? throw new BusinessException($"用户 {input.UserId} 不存在");

            // 验证旧密码是否正确
            if (user.Password != EncryptHelper.EncryptPassword(input.OldPassword))
                throw new BusinessException("旧密码不正确");


            // 更新密码
            user.Password = EncryptHelper.EncryptPassword(input.NewPassword);

            await _userDLL.UpdateAsync(user);
        }

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="input">密码重置信息DTO</param>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task ResetPasswordAsync(ResetPasswordDto input)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = input.UserId })
                ?? throw new BusinessException($"用户 {input.UserId} 不存在");

            // 重置密码
            user.Password = EncryptHelper.EncryptPassword(input.NewPassword);

            await _userDLL.UpdateAsync(user);
        }
        /// <summary>
        /// 分配用户角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleIds">角色ID列表</param>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task AssignRolesAsync(string userId, List<string> roleIds, CurrentUserInfoDto currentUserInfo)
        {
            // 验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = userId })
                ?? throw new BusinessException($"用户 {userId} 不存在");

            // 先移除用户的所有角色
            await RemoveUserRolesAsync(userId);

            // 添加新的角色关联
            if (roleIds != null && roleIds.Count > 0)
            {
                var userRoles = roleIds.Select(roleId => new SysUserRole
                {
                    UserId = userId,
                    RoleId = roleId
                }).ToList();

                // 初始化基础字段
                foreach (var userRole in userRoles)
                {
                    userRole.InitializeForAdd(currentUserInfo);
                }

                await _userDLL.AddUserRolesAsync(userRoles);
            }
        }

        /// <summary>
        /// 获取用户的角色列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户的角色列表</returns>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task<List<SysRole>> GetUserRolesAsync(string userId)
        {
            // 验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = userId })
                ?? throw new BusinessException($"用户 {userId} 不存在");

            return await _userDLL.GetUserRolesAsync(user.UserId);
        }

        /// <summary>
        /// 移除用户的所有角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task RemoveUserRolesAsync(string userId)
        {
            // 验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = userId })
                ?? throw new BusinessException($"用户 {userId} 不存在");

            await _userDLL.RemoveUserRolesAsync(user.UserId);
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="loginRequest">登录请求信息</param>
        /// <returns>登录响应信息</returns>
        public async Task<LoginResponseDto> LoginAsync(LoginRequestDto loginRequest)
        {
            // 1. 验证用户名和密码
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserName = loginRequest.Username })
                ?? throw new BusinessException("用户不存在");

            if (!string.Equals(loginRequest.Password, user.Password))
                throw new BusinessException("密码错误");

            if (user.Status != 1)
                throw new BusinessException("用户已被禁用");

            // 2. 生成登录响应
            var loginResponse = await GenerateLoginResponseAsync(user);

        
            // 4. 更新用户最后登录信息
            await UpdateUserLoginInfoAsync(user);

            return loginResponse;
        }

        /// <summary>
        /// 生成登录响应信息
        /// </summary>
        /// <param name="user">用户</param>
        /// <returns>登录响应信息</returns>
        public async Task<LoginResponseDto> GenerateLoginResponseAsync(SysUser user)
        {
            // 1. 获取用户角色和权限
            var roles = await GetUserRolesAsync(user.UserId);
            var permissions = await _permissionService.GetUserPermissionsAsync(user.UserId);

            // 2. 构建用户信息
            var userInfo = new UserInfoDto
            {
                UserId = user.UserId,
                Username = user.UserName,
                NickName = user.RealName,
                Roles = [.. roles.Select(r => r.Code)],
                Permissions = permissions
            };

            // 3. 生成Token
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.UserId),
                new(ClaimTypes.Name, user.UserName)
            };
            claims.AddRange(userInfo.Roles.Select(role => new Claim(ClaimTypes.Role, role)));

            var accessToken = JWTHelper.CreateJwt(claims);

            // 更新登录状态
            RedisHelper.Set(GetUserKey(user.UserId), accessToken, LOGIN_STATUS_EXPIRY);



            return new LoginResponseDto
            {
                AccessToken = accessToken,
                UserInfo = userInfo
            };
        }

        /// <summary>
        /// 更新用户最后登录信息
        /// </summary>
        /// <param name="user">用户</param>
        /// <returns>更新后的用户</returns>
        private async Task UpdateUserLoginInfoAsync(SysUser user)
        {
            user.LastLoginTime = DateTime.Now;
            user.LastLoginIp = ""; // 这里可以添加获取IP的逻辑
            await _userDLL.UpdateAsync(user);
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息DTO</returns>
        public async Task<UserInfoDto> GetUserInfoAsync(string userId)
        {
            // 查询用户基本信息
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = userId }) ?? throw new BusinessException("用户不存在");

            // 获取用户角色
            var roles = await GetUserRolesAsync(userId);

            // 获取用户权限
            var permissions = await _permissionService.GetUserPermissionsAsync(userId);

            // 构建用户信息DTO
            return new UserInfoDto
            {
                UserId = userId,
                Username = user.UserName,
                NickName = user.RealName,
                Roles = [.. roles.Select(r => r.Code)],
                Permissions = permissions
            };
        }

        /// <summary>
        /// 用户登出
        /// </summary>
        /// <param name="userId">用户ID</param>
        public static void Logout(string userId) => ClearUserLoginStatus(userId);

    }
}