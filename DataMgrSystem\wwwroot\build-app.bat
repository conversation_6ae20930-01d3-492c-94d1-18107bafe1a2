@echo off
echo 正在打包 .NET Core 应用...
cd /d %~dp0

echo 选择发布模式:
echo 1. Debug
echo 2. Release
set /p mode=请输入选择 (1 或 2): 

if "%mode%"=="1" (
    set buildMode=Debug
) else if "%mode%"=="2" (
    set buildMode=Release
) else (
    echo 无效选择，使用默认 Release 模式
    set buildMode=Release
)

echo 正在执行 %buildMode% 模式打包...
dotnet publish AdminProjectTemplate -c %buildMode% -o ./publish

if %errorlevel% equ 0 (
    echo 应用打包成功！输出目录: %~dp0publish
    start explorer "%~dp0publish"
) else (
    echo 打包过程中发生错误，请检查日志。
)

pause 