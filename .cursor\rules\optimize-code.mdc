---
description: 
globs: 
alwaysApply: false
---
Rule Name: code-optimization

Description:
# Code Optimization Guidelines

## General Principles
- Write clean, concise, and elegant code
- Follow DRY (Don't Repeat Yourself) principles
- Maintain consistent coding style across the project
- Remove redundant and unused code
- Optimize for readability and maintainability

## Project-Specific Standards
1. **Architecture Pattern**
   - Follow the established three-tier architecture (DAL/BLL/API)
   - Use proper separation of concerns between layers
   - Utilize the existing base classes and interfaces

2. **Common Utilities**
   - Leverage existing utility classes in `Common` project
   - Use standardized helpers for common operations
   - Centralize shared logic in appropriate locations

3. **Code Style**
   - Inherit from appropriate base classes (`BaseController`, `Ba<PERSON>Controller`)
   - Use standard response patterns (`Result<T>`, `Result`)
   - Follow established naming conventions

4. **Performance Optimization**
   - Use async/await for I/O operations
   - Implement proper caching strategies
   - Optimize LINQ queries and database operations
   - Use efficient data structures

5. **Best Practices**
   - Implement proper exception handling
   - Use dependency injection
   - Follow SOLID principles
   - Write unit-testable code

6. **API Design**
   - Use consistent API response formats
   - Implement proper validation
   - Follow RESTful conventions
   - Use appropriate HTTP status codes

7. **Security**
   - Use proper authentication/authorization
   - Implement secure data handling
   - Follow security best practices

## Implementation Guidelines
- Use C# latest features appropriately
- Implement proper logging and monitoring
- Write clear XML documentation
- Use meaningful variable and method names

- Keep methods focused and concise