using Common.Autofac;
using Common.Exceptions;
using DAL.Databases;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Entity.Extensions;

namespace BLL.SysService
{
    /// <summary>
    /// 菜单服务实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class SysMenuService(SysMenuDAL menuDLL)
    {
        /// <summary>
        /// 菜单数据访问层
        /// </summary>
        private readonly SysMenuDAL _menuDLL = menuDLL;

        /// <summary>
        /// 创建菜单
        /// </summary>
        /// <param name="input">创建菜单信息</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>菜单ID</returns>
        /// <exception cref="BusinessException">父级菜单不存在</exception>
        public async Task<string> CreateAsync(CreateMenuDto input, CurrentUserInfoDto currentUserInfo)
        {

            SysMenu parentMenu = new();
            // 检查父级菜单是否存在
            if (!string.IsNullOrEmpty(input.ParentId))
            {
                if (input.ParentId != "")
                {
                    parentMenu = await _menuDLL.GetFirstAsync(new SysMenuDAL.Queryable { Id = input.ParentId })
                        ?? throw new BusinessException($"父级菜单 {input.ParentId} 不存在");
                }
            }

            // 创建菜单实体
            var menu = new SysMenu
            {
                ParentId = parentMenu.Id,
                Name = input.Name,
                Path = input.Path,
                Component = input.Component,
                Perms = input.Perms,
                Icon = input.Icon,
                Type = input.Type,
                OrderNum = input.OrderNum,
                Visible = input.Visible,
                Status = input.Status,
            };

            // 初始化基础字段
            menu.InitializeForAdd(currentUserInfo);

            await _menuDLL.AddAsync(menu);

            // 清空权限缓存
            SysPermissionService.ClearPermissionCache();
            return menu.Id;
        }

        /// <summary>
        /// 更新菜单
        /// </summary>
        /// <param name="input">更新菜单信息</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns></returns>
        /// <exception cref="BusinessException">菜单不存在</exception>
        public async Task UpdateAsync(UpdateMenuDto input, CurrentUserInfoDto currentUserInfo)
        {
            // 获取并验证菜单是否存在
            var menu = await _menuDLL.GetFirstAsync(new SysMenuDAL.Queryable { Id = input.Id })
                ?? throw new BusinessException($"菜单 {input.Id} 不存在");

            // 检查父级菜单是否存在
            if (!string.IsNullOrEmpty(input.ParentId))
            {
                if (input.ParentId == input.Id) throw new BusinessException("父级菜单不能是自己");

                var parentMenu = await _menuDLL.GetFirstAsync(new SysMenuDAL.Queryable { Id = input.ParentId })
                    ?? throw new BusinessException($"父级菜单 {input.ParentId} 不存在");
                // 更新菜单信息
                menu.ParentId = parentMenu.Id;
            }
            else menu.ParentId = input.ParentId; // 更新菜单信息

            menu.Name = input.Name;
            menu.Path = input.Path;
            menu.Component = input.Component;
            menu.Perms = input.Perms;
            menu.Icon = input.Icon;
            menu.Type = input.Type;
            menu.OrderNum = input.OrderNum;
            menu.Visible = input.Visible;
            menu.Status = input.Status;

            // 初始化更新字段
            menu.InitializeForUpdate(currentUserInfo);

            await _menuDLL.UpdateAsync(menu);
            // 清空权限缓存
            SysPermissionService.ClearPermissionCache();
        }

        /// <summary>
        /// 删除菜单
        /// </summary>
        /// <param name="id">菜单ID</param>
        /// <exception cref="BusinessException">菜单不存在</exception>
        public async Task DeleteAsync(string id)
        {
            // 获取并验证菜单是否存在
            var menu = await _menuDLL.GetFirstAsync(new SysMenuDAL.Queryable { Id = id })
                ?? throw new BusinessException($"菜单 {id} 不存在");

            // 检查是否有子菜单
            if (await _menuDLL.HasChildrenAsync(id))
                throw new BusinessException("存在子菜单，无法删除");


            await _menuDLL.DeleteAsync(menu);
            // 清空权限缓存
            SysPermissionService.ClearPermissionCache();
        }

        /// <summary>
        /// 获取菜单详情
        /// </summary>
        /// <param name="id">菜单ID</param>
        /// <returns>菜单详情</returns>
        /// <exception cref="BusinessException">菜单不存在</exception>
        public async Task<MenuDto> GetAsync(string id)
        {
            var menu = await _menuDLL.GetFirstAsync(new SysMenuDAL.Queryable { Id = id })
                ?? throw new BusinessException($"菜单 {id} 不存在");

            return new MenuDto
            {
                Id = menu.Id,
                ParentId = menu.ParentId,
                Name = menu.Name,
                Path = menu.Path,
                Component = menu.Component,
                Perms = menu.Perms,
                Icon = menu.Icon,
                Type = menu.Type,
                OrderNum = menu.OrderNum,
                Visible = menu.Visible,
                Status = menu.Status
            };
        }

        /// <summary>
        /// 获取菜单树
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>菜单树</returns>
        public async Task<List<MenuDto>> GetTreeAsync(SysMenuDAL.Queryable query)
        => BuildTree(await _menuDLL.GetListAsync(query));


        /// <summary>
        /// 获取用户菜单树
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>菜单树</returns>
        public async Task<List<MenuDto>> GetUserMenuTreeAsync(string userId)
        {
            var menus = await _menuDLL.GetUserALLMenusAsync(userId);
            return BuildTree([.. menus.Where(m => m.Type != 2)]); // 排除按钮
        }

        /// <summary>
        /// 获取用户权限列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限列表</returns>
        public async Task<List<string>> GetUserPermissionsAsync(string userId)
        {
            var menus = await _menuDLL.GetUserALLMenusAsync(userId);
            return [.. menus.Where(m => !string.IsNullOrEmpty(m.Perms))
                       .SelectMany(m => m.Perms!.Split(',', StringSplitOptions.RemoveEmptyEntries))
                       .Distinct()];
        }

        /// <summary>
        /// 获取所有菜单
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>菜单列表</returns>
        public async Task<List<MenuDto>> GetAllAsync(SysMenuDAL.Queryable query)
        {
            var menus = await _menuDLL.GetListAsync(query);

            return [.. menus.Select(m => new MenuDto
            {
                Id = m.Id,
                ParentId = m.ParentId,
                Name = m.Name,
                Path = m.Path,
                Component = m.Component,
                Perms = m.Perms,
                Icon = m.Icon,
                Type = m.Type,
                OrderNum = m.OrderNum,
                Visible = m.Visible,
                Status = m.Status
            })];
        }

        /// <summary>
        /// 获取用户的菜单权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>菜单列表</returns>
        public async Task<List<MenuDto>> GetUserMenusAsync(string userId)
        {
            var menus = await _menuDLL.GetUserALLMenusAsync(userId);

            return [.. menus.Select(m => new MenuDto
            {
                Id = m.Id,
                ParentId = m.ParentId,
                Name = m.Name,
                Path = m.Path,
                Component = m.Component,
                Perms = m.Perms,
                Icon = m.Icon,
                Type = m.Type,
                OrderNum = m.OrderNum,
                Visible = m.Visible,
                Status = m.Status
            })];
        }



        /// <summary>
        /// 构建菜单树
        /// </summary>
        /// <param name="menus">菜单列表</param>
        /// <param name="parentId">父级ID</param>
        /// <returns>菜单树</returns>
        public static List<MenuDto> BuildTree(List<SysMenu> menus, string parentId = "")
        {
            var tree = new List<MenuDto>();

            var children = menus.Where(m => m.ParentId == parentId)
                              .OrderBy(m => m.OrderNum)
                              .ToList();

            foreach (var child in children)
            {
                var node = new MenuDto
                {
                    Id = child.Id,
                    ParentId = child.ParentId,
                    Name = child.Name,
                    Path = child.Path,
                    Component = child.Component,
                    Perms = child.Perms,
                    Icon = child.Icon,
                    Type = child.Type,
                    OrderNum = child.OrderNum,
                    Visible = child.Visible,
                    Status = child.Status,
                    Children = BuildTree(menus, child.Id)
                };

                tree.Add(node);
            }

            return tree;
        }

        /// <summary>
        /// 根据ID列表获取菜单
        /// </summary>
        /// <param name="menuIds">菜单ID列表</param>
        /// <returns>菜单列表</returns>
        public async Task<List<MenuDto>> GetMenusByIdsAsync(List<string> menuIds)
        {
            var menus = await _menuDLL.GetMenusByIdsAsync(menuIds);

            return [.. menus.Select(m => new MenuDto
            {
                Id = m.Id,
                ParentId = m.ParentId,
                Name = m.Name,
                Path = m.Path,
                Component = m.Component,
                Perms = m.Perms,
                Icon = m.Icon,
                Type = m.Type,
                OrderNum = m.OrderNum,
                Visible = m.Visible,
                Status = m.Status
            })];
        }
    }
}