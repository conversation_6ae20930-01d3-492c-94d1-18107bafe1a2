namespace Entity.Dto
{
    /// <summary>
    /// 权限方法元数据DTO
    /// </summary>
    public class PermissionMetadataDto
    {
        /// <summary>
        /// 控制器名称
        /// </summary>
        public string ControllerName { get; set; } = string.Empty;

        /// <summary>
        /// 方法名称
        /// </summary>
        public string MethodName { get; set; } = string.Empty;

        /// <summary>
        /// HTTP方法（GET、POST等）
        /// </summary>
        public string HttpMethod { get; set; } = string.Empty;

        /// <summary>
        /// 路由
        /// </summary>
        public string Route { get; set; } = string.Empty;

        /// <summary>
        /// 权限编码
        /// </summary>
        public string PermissionCode { get; set; } = string.Empty;

        /// <summary>
        /// 权限描述
        /// </summary>
        public string PermissionDescription { get; set; } = string.Empty;
    }
}