using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.CardDto
{
    /// <summary>
    /// 卡片核销DTO
    /// </summary>
    public class UseCardDto
    {
        /// <summary>
        /// 卡号
        /// </summary>
        [Required(ErrorMessage = "卡号不能为空")]
        public string CardNo { get; set; } = string.Empty;

        /// <summary>
        /// 卡片密码
        /// </summary>
        [Required(ErrorMessage = "卡片密码不能为空")]
        public string CardPwd { get; set; } = string.Empty;

        /// <summary>
        /// 玩家ID
        /// </summary>
        [Required(ErrorMessage = "玩家ID不能为空")]
        public string PlayerId { get; set; } = string.Empty;

        /// <summary>
        /// 操作IP
        /// </summary>
        [Required(ErrorMessage = "操作IP不能为空")]
        public string Ip { get; set; } = string.Empty;
    }
}