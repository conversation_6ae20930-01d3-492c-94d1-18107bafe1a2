using Common.Autofac;
using DAL.Databases;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Entity.Extensions;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.SysDAL
{
    /// <summary>
    /// 权限数据访问层实现
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class SysPermissionDAL(MyContext context) : BaseQueryDLL<SysPermission, SysPermissionDAL.Queryable>(context)
    {

        /// <summary>
        /// 用户查询条件模型类
        /// 定义了用户查询时可用的过滤条件
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            [Query(QueryOperator.等于)]
            public string? SubjectId { get; set; }

            [Query(QueryOperator.等于)]
            public string? ObjectId { get; set; }
        }



        // 获取权限列表
        public Task<List<SysPermission>> GetPermissionsAsync(List<string> SubjectId)
        => _dbContext.Set<SysPermission>().Where(m => SubjectId.Contains(m.SubjectId)).ToListAsync();

        /// <summary>
        /// 通过权限ID获取菜单 
        /// </summary>
        /// <param name="menuIds">菜单ID列表</param>
        /// <returns>菜单列表</returns>
        public Task<List<SysMenu>> GetMenusByPermissionIdAsync(List<string> menuIds)
        => _dbContext.Set<SysMenu>()
                .Where(m => menuIds.Contains(m.Id))
                .ToListAsync();

        /// <summary>
        /// 通过权限ID获取按钮
        /// </summary>
        /// <param name="buttonIds">按钮ID列表</param>
        /// <returns>按钮列表</returns>
        public Task<List<SysButton>> GetButtonsByPermissionIdAsync(List<string> buttonIds)
        => _dbContext.Set<SysButton>()
                .Where(b => buttonIds.Contains(b.Id))
                .ToListAsync();


        /// <summary>
        /// 分配权限
        /// </summary>
        /// <param name="assignPermissions_ReqDto">分配权限请求DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>分配结果</returns>
        public async Task<bool> AssignPermissionsAsync(AssignPermissions_ReqDto assignPermissions_ReqDto, CurrentUserInfoDto currentUserInfo)
        {
            // 获取当前已存在的权限关系
            var existingPermissions = await GetListAsync(new Queryable { SubjectId = assignPermissions_ReqDto.SubjectId });

            // 获取需要删除的权限（在现有权限中但不在新权限列表中的）
            var permissionsToDelete = existingPermissions
                .Where(ep => !assignPermissions_ReqDto.PermissionCodes.Contains(ep.ObjectId))
                .ToList();

            // 获取需要新增的权限（在新权限列表中但不在现有权限中的）
            var existingPermissionCodes = existingPermissions.Select(ep => ep.ObjectId).ToList();
            var permissionsToAdd = assignPermissions_ReqDto.PermissionCodes
                .Where(pc => !existingPermissionCodes.Contains(pc))
                .Select(pc => new SysPermission
                {
                    SubjectId = assignPermissions_ReqDto.SubjectId,
                    ObjectId = pc
                })
                .ToList();

            // 删除不需要的权限
            if (permissionsToDelete.Count != 0)
                _dbContext.Set<SysPermission>().RemoveRange(permissionsToDelete);

            // 添加新的权限
            if (permissionsToAdd.Count != 0)
            {
                // 初始化基础字段
                foreach (var permission in permissionsToAdd)
                {
                    permission.InitializeForAdd(currentUserInfo);
                }
                await _dbContext.Set<SysPermission>().AddRangeAsync(permissionsToAdd);
            }

            // 保存更改
            await _dbContext.SaveChangesAsync();
            // 清空权限缓存
            return true;
        }

    }
}