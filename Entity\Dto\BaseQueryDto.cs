namespace Entity.Dto;

/// <summary>
/// 基础查询DTO
/// </summary>
public class BaseQueryDto
{
    /// <summary>   
    /// 页码
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 每页条数
    /// </summary>
    public int PageSize { get; set; } = 10;
    /// <summary>
    /// 排序字段
    /// </summary>
    public string? OrderField { get; set; }
    /// <summary>
    /// 是否升序
    /// </summary>
    public bool IsAsc { get; set; } = false;
}


/// <summary>
/// 当前用户登陆信息
/// </summary>
public class CurrentUserInfoDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;
    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = string.Empty;
}